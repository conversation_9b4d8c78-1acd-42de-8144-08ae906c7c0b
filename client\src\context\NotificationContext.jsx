import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { toast } from 'react-toastify';

const NotificationContext = createContext();

const initialState = {
  notifications: [],
  unreadCount: 0,
};

const notificationReducer = (state, action) => {
  switch (action.type) {
    case 'ADD_NOTIFICATION':
      const newNotification = {
        id: Date.now() + Math.random(),
        ...action.payload,
        timestamp: new Date(),
        read: false,
      };
      return {
        ...state,
        notifications: [newNotification, ...state.notifications],
        unreadCount: state.unreadCount + 1,
      };
    
    case 'MARK_AS_READ':
      return {
        ...state,
        notifications: state.notifications.map(notification =>
          notification.id === action.payload
            ? { ...notification, read: true }
            : notification
        ),
        unreadCount: Math.max(0, state.unreadCount - 1),
      };
    
    case 'MARK_ALL_AS_READ':
      return {
        ...state,
        notifications: state.notifications.map(notification => ({
          ...notification,
          read: true,
        })),
        unreadCount: 0,
      };
    
    case 'REMOVE_NOTIFICATION':
      const notificationToRemove = state.notifications.find(n => n.id === action.payload);
      return {
        ...state,
        notifications: state.notifications.filter(n => n.id !== action.payload),
        unreadCount: notificationToRemove && !notificationToRemove.read 
          ? state.unreadCount - 1 
          : state.unreadCount,
      };
    
    case 'CLEAR_ALL':
      return {
        ...state,
        notifications: [],
        unreadCount: 0,
      };
    
    case 'LOAD_NOTIFICATIONS':
      const unread = action.payload.filter(n => !n.read).length;
      return {
        ...state,
        notifications: action.payload,
        unreadCount: unread,
      };
    
    default:
      return state;
  }
};

export const NotificationProvider = ({ children }) => {
  const [state, dispatch] = useReducer(notificationReducer, initialState);

  // Load notifications from localStorage on mount
  useEffect(() => {
    const savedNotifications = localStorage.getItem('notifications');
    if (savedNotifications) {
      try {
        const notifications = JSON.parse(savedNotifications).map(n => ({
          ...n,
          timestamp: new Date(n.timestamp),
        }));
        dispatch({ type: 'LOAD_NOTIFICATIONS', payload: notifications });
      } catch (error) {
        console.error('Error loading notifications:', error);
      }
    }
  }, []);

  // Save notifications to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('notifications', JSON.stringify(state.notifications));
  }, [state.notifications]);

  const addNotification = (notification) => {
    dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
    
    // Show toast notification
    const toastOptions = {
      position: 'top-right',
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    };

    switch (notification.type) {
      case 'success':
        toast.success(notification.message, toastOptions);
        break;
      case 'error':
        toast.error(notification.message, toastOptions);
        break;
      case 'warning':
        toast.warning(notification.message, toastOptions);
        break;
      case 'info':
      default:
        toast.info(notification.message, toastOptions);
        break;
    }
  };

  const markAsRead = (id) => {
    dispatch({ type: 'MARK_AS_READ', payload: id });
  };

  const markAllAsRead = () => {
    dispatch({ type: 'MARK_ALL_AS_READ' });
  };

  const removeNotification = (id) => {
    dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
  };

  const clearAll = () => {
    dispatch({ type: 'CLEAR_ALL' });
  };

  // Predefined notification templates
  const notificationTemplates = {
    eventRegistration: (eventTitle) => ({
      type: 'success',
      title: 'Registration Successful',
      message: `You have successfully registered for ${eventTitle}`,
      icon: 'fas fa-check-circle',
      category: 'registration',
    }),
    
    eventUnregistration: (eventTitle) => ({
      type: 'info',
      title: 'Unregistered from Event',
      message: `You have unregistered from ${eventTitle}`,
      icon: 'fas fa-times-circle',
      category: 'registration',
    }),
    
    eventReminder: (eventTitle, timeUntil) => ({
      type: 'warning',
      title: 'Event Reminder',
      message: `${eventTitle} starts in ${timeUntil}`,
      icon: 'fas fa-clock',
      category: 'reminder',
    }),
    
    profileUpdate: () => ({
      type: 'success',
      title: 'Profile Updated',
      message: 'Your profile has been successfully updated',
      icon: 'fas fa-user-check',
      category: 'profile',
    }),
    
    passwordChange: () => ({
      type: 'success',
      title: 'Password Changed',
      message: 'Your password has been successfully changed',
      icon: 'fas fa-key',
      category: 'security',
    }),
    
    wishlistAdd: (eventTitle) => ({
      type: 'info',
      title: 'Added to Wishlist',
      message: `${eventTitle} has been added to your wishlist`,
      icon: 'fas fa-heart',
      category: 'wishlist',
    }),
    
    wishlistRemove: (eventTitle) => ({
      type: 'info',
      title: 'Removed from Wishlist',
      message: `${eventTitle} has been removed from your wishlist`,
      icon: 'fas fa-heart-broken',
      category: 'wishlist',
    }),
  };

  const addTemplateNotification = (template, ...args) => {
    if (notificationTemplates[template]) {
      const notification = notificationTemplates[template](...args);
      addNotification(notification);
    }
  };

  const value = {
    notifications: state.notifications,
    unreadCount: state.unreadCount,
    addNotification,
    addTemplateNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export default NotificationContext;
