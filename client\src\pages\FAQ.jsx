import React, { useState } from 'react';
import { Container, Row, Col, Card, Accordion, Form, Alert, Badge } from 'react-bootstrap';

const FAQ = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  const faqData = [
    {
      id: 1,
      category: 'General',
      question: 'What is ALTIUS 2K25?',
      answer: 'ALTIUS 2K25 is the annual college fest that brings together technology, culture, sports, and creativity. It features various competitions, performances, workshops, and networking opportunities for students.'
    },
    {
      id: 2,
      category: 'Registration',
      question: 'How do I register for events?',
      answer: 'To register for events, you need to create an account on our website, browse the events page, and click the "Register" button on any event you\'re interested in. Make sure to complete your profile for a smooth registration process.'
    },
    {
      id: 3,
      category: 'Registration',
      question: 'Can I register for multiple events?',
      answer: 'Yes! You can register for as many events as you want, as long as they don\'t have conflicting schedules. Check the event calendar to avoid time conflicts.'
    },
    {
      id: 4,
      category: 'Registration',
      question: 'Is there a registration fee?',
      answer: 'Most events are free to participate. However, some special events or workshops might have a nominal fee. This will be clearly mentioned on the event details page.'
    },
    {
      id: 5,
      category: 'Events',
      question: 'What types of events are available?',
      answer: 'We have events across multiple categories: Technical (coding, robotics, AI/ML), Cultural (music, dance, drama), Sports (cricket, football, badminton), Literary (debate, poetry), Gaming (esports, board games), and many more.'
    },
    {
      id: 6,
      category: 'Events',
      question: 'Can I participate in team events?',
      answer: 'Yes! Many events support both individual and team participation. Team size limits and formation details are mentioned in each event\'s rules and regulations.'
    },
    {
      id: 7,
      category: 'Events',
      question: 'What if I need to cancel my registration?',
      answer: 'You can cancel your registration by going to your dashboard and clicking "Unregister" for the specific event. Please note that cancellation might not be possible close to the event date.'
    },
    {
      id: 8,
      category: 'Technical',
      question: 'What should I bring for technical events?',
      answer: 'For most technical events, you\'ll need to bring your laptop with necessary software pre-installed. Specific requirements are listed in each event\'s description. We provide power outlets and WiFi.'
    },
    {
      id: 9,
      category: 'Technical',
      question: 'Are there any prerequisites for technical events?',
      answer: 'Prerequisites vary by event. Some beginner-friendly events have no requirements, while advanced competitions might require specific skills. Check the event details for requirements and difficulty level.'
    },
    {
      id: 10,
      category: 'Venue',
      question: 'Where is ALTIUS 2K25 being held?',
      answer: 'ALTIUS 2K25 is held at our college campus. Specific venue details for each event are mentioned in the event descriptions. Campus maps and directions will be provided closer to the event dates.'
    },
    {
      id: 11,
      category: 'Venue',
      question: 'Is parking available?',
      answer: 'Yes, free parking is available on campus for all participants. Please follow the parking guidelines and designated areas to avoid any inconvenience.'
    },
    {
      id: 12,
      category: 'Prizes',
      question: 'What prizes can I win?',
      answer: 'Prizes vary by event and include cash rewards, certificates, trophies, and exciting goodies. Special recognition is given to overall performers across multiple categories.'
    },
    {
      id: 13,
      category: 'Prizes',
      question: 'How are winners selected?',
      answer: 'Winners are selected by expert judges based on predefined criteria specific to each event. Judging criteria are transparent and mentioned in the event rules.'
    },
    {
      id: 14,
      category: 'Contact',
      question: 'Who can I contact for help?',
      answer: 'For general queries, email <NAME_EMAIL>. For event-specific questions, contact details are provided on each event page. You can also reach out to our student coordinators.'
    },
    {
      id: 15,
      category: 'Contact',
      question: 'How do I report technical issues with the website?',
      answer: 'If you encounter any technical issues with the website, please email <NAME_EMAIL> with a detailed description of the problem and screenshots if possible.'
    }
  ];

  const categories = ['All', ...new Set(faqData.map(faq => faq.category))];

  const filteredFAQs = faqData.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getCategoryColor = (category) => {
    const colors = {
      'General': 'primary',
      'Registration': 'success',
      'Events': 'warning',
      'Technical': 'info',
      'Venue': 'secondary',
      'Prizes': 'danger',
      'Contact': 'dark'
    };
    return colors[category] || 'primary';
  };

  return (
    <div className="faq-page py-3 py-md-5">
      <Container fluid className="px-3 px-md-4">
        {/* Header */}
        <Row className="mb-5">
          <Col className="text-center">
            <h1 className="display-4 fw-bold text-dark mb-3">
              <i className="fas fa-question-circle text-primary me-3"></i>
              Frequently Asked Questions
            </h1>
            <p className="lead text-muted">
              Find answers to common questions about ALTIUS 2K25
            </p>
          </Col>
        </Row>

        {/* Search and Filter */}
        <Row className="mb-4">
          <Col md={8} className="mb-3">
            <Form.Control
              type="text"
              placeholder="Search FAQs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              size="lg"
            />
          </Col>
          <Col md={4} className="mb-3">
            <Form.Select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              size="lg"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'All' ? 'All Categories' : category}
                </option>
              ))}
            </Form.Select>
          </Col>
        </Row>

        {/* Results Info */}
        <Row className="mb-4">
          <Col>
            <Alert variant="info" className="d-flex align-items-center">
              <i className="fas fa-info-circle me-2"></i>
              Showing {filteredFAQs.length} of {faqData.length} questions
              {searchTerm && ` for "${searchTerm}"`}
              {selectedCategory !== 'All' && ` in ${selectedCategory}`}
            </Alert>
          </Col>
        </Row>

        {/* FAQ Content */}
        <Row>
          <Col>
            {filteredFAQs.length === 0 ? (
              <Card className="text-center py-5">
                <Card.Body>
                  <i className="fas fa-search display-1 text-muted mb-4"></i>
                  <h3 className="fw-bold mb-3">No FAQs Found</h3>
                  <p className="text-muted mb-4">
                    Try adjusting your search terms or category filter.
                  </p>
                  <div className="d-flex gap-2 justify-content-center">
                    <button
                      className="btn btn-outline-primary"
                      onClick={() => setSearchTerm('')}
                    >
                      Clear Search
                    </button>
                    <button
                      className="btn btn-outline-secondary"
                      onClick={() => setSelectedCategory('All')}
                    >
                      All Categories
                    </button>
                  </div>
                </Card.Body>
              </Card>
            ) : (
              <Accordion defaultActiveKey="0">
                {filteredFAQs.map((faq, index) => (
                  <Accordion.Item eventKey={index.toString()} key={faq.id}>
                    <Accordion.Header>
                      <div className="d-flex align-items-center w-100">
                        <Badge 
                          bg={getCategoryColor(faq.category)} 
                          className="me-3"
                        >
                          {faq.category}
                        </Badge>
                        <span className="fw-semibold">{faq.question}</span>
                      </div>
                    </Accordion.Header>
                    <Accordion.Body>
                      <p className="mb-0 text-muted">{faq.answer}</p>
                    </Accordion.Body>
                  </Accordion.Item>
                ))}
              </Accordion>
            )}
          </Col>
        </Row>

        {/* Contact Section */}
        <Row className="mt-5">
          <Col>
            <Card className="bg-light">
              <Card.Body className="text-center py-4">
                <h4 className="fw-bold mb-3">
                  <i className="fas fa-envelope text-primary me-2"></i>
                  Still have questions?
                </h4>
                <p className="text-muted mb-4">
                  Can't find what you're looking for? We're here to help!
                </p>
                <div className="d-flex flex-column flex-md-row gap-3 justify-content-center">
                  <a
                    href="mailto:<EMAIL>"
                    className="btn btn-primary"
                  >
                    <i className="fas fa-envelope me-2"></i>
                    Email Us
                  </a>
                  <a
                    href="tel:+919876543210"
                    className="btn btn-outline-primary"
                  >
                    <i className="fas fa-phone me-2"></i>
                    Call Us
                  </a>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default FAQ;
