import React, { useState, useEffect, useRef } from 'react';
import { Form, Button, Card, Badge, Dropdown, InputGroup, ListGroup } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';

const AdvancedSearch = ({ events = [], onSearch, placeholder = "Search events..." }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    category: 'All',
    dateRange: 'All',
    venue: 'All',
    registrationStatus: 'All',
    difficulty: 'All'
  });
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  const searchRef = useRef(null);
  const navigate = useNavigate();

  // Categories and filter options
  const categories = ['All', 'Technical', 'Cultural', 'Sports', 'Literary', 'Gaming', 'Other'];
  const dateRanges = ['All', 'Today', 'This Week', 'This Month', 'Upcoming'];
  const venues = ['All', ...new Set(events.map(event => event.venue).filter(Boolean))];
  const registrationStatuses = ['All', 'Open', 'Closed', 'Full'];
  const difficulties = ['All', 'Beginner', 'Intermediate', 'Advanced'];

  useEffect(() => {
    // Load recent searches from localStorage
    const saved = localStorage.getItem('recent_searches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  useEffect(() => {
    // Generate suggestions based on search term
    if (searchTerm.length > 0) {
      const filtered = events
        .filter(event => 
          event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          event.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
          event.venue.toLowerCase().includes(searchTerm.toLowerCase())
        )
        .slice(0, 5);
      
      setSuggestions(filtered);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [searchTerm, events]);

  const handleSearch = (term = searchTerm) => {
    if (!term.trim()) return;

    // Save to recent searches
    const newRecentSearches = [term, ...recentSearches.filter(s => s !== term)].slice(0, 5);
    setRecentSearches(newRecentSearches);
    localStorage.setItem('recent_searches', JSON.stringify(newRecentSearches));

    // Perform search
    const searchParams = {
      term: term.trim(),
      ...filters
    };

    if (onSearch) {
      onSearch(searchParams);
    } else {
      // Navigate to events page with search params
      const params = new URLSearchParams();
      params.set('search', term.trim());
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== 'All') {
          params.set(key, value);
        }
      });
      navigate(`/events?${params.toString()}`);
    }

    setShowSuggestions(false);
  };

  const handleSuggestionClick = (event) => {
    setSearchTerm(event.title);
    handleSearch(event.title);
  };

  const handleRecentSearchClick = (term) => {
    setSearchTerm(term);
    handleSearch(term);
  };

  const handleFilterChange = (filterType, value) => {
    const newFilters = { ...filters, [filterType]: value };
    setFilters(newFilters);
    
    // Auto-search if there's a search term
    if (searchTerm.trim()) {
      const searchParams = {
        term: searchTerm.trim(),
        ...newFilters
      };
      if (onSearch) {
        onSearch(searchParams);
      }
    }
  };

  const clearFilters = () => {
    setFilters({
      category: 'All',
      dateRange: 'All',
      venue: 'All',
      registrationStatus: 'All',
      difficulty: 'All'
    });
  };

  const clearSearch = () => {
    setSearchTerm('');
    setShowSuggestions(false);
    if (onSearch) {
      onSearch({ term: '', ...filters });
    }
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== 'All').length;
  };

  return (
    <div className="advanced-search position-relative">
      {/* Main Search Bar */}
      <InputGroup size="lg" className="mb-3">
        <Form.Control
          ref={searchRef}
          type="text"
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          onFocus={() => setShowSuggestions(true)}
          className="border-end-0"
        />
        {searchTerm && (
          <Button
            variant="outline-secondary"
            onClick={clearSearch}
            className="border-start-0 border-end-0"
          >
            <i className="fas fa-times"></i>
          </Button>
        )}
        <Button
          variant="primary"
          onClick={() => handleSearch()}
        >
          <i className="fas fa-search"></i>
        </Button>
        <Button
          variant="outline-secondary"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className={`position-relative ${getActiveFiltersCount() > 0 ? 'text-warning' : ''}`}
        >
          <i className="fas fa-filter"></i>
          {getActiveFiltersCount() > 0 && (
            <Badge
              bg="warning"
              pill
              className="position-absolute top-0 start-100 translate-middle"
              style={{ fontSize: '0.6rem' }}
            >
              {getActiveFiltersCount()}
            </Badge>
          )}
        </Button>
      </InputGroup>

      {/* Search Suggestions */}
      {showSuggestions && (searchTerm.length > 0 || recentSearches.length > 0) && (
        <Card className="position-absolute w-100 shadow-lg" style={{ zIndex: 1000 }}>
          <Card.Body className="p-0">
            {/* Event Suggestions */}
            {suggestions.length > 0 && (
              <>
                <div className="px-3 py-2 bg-light border-bottom">
                  <small className="text-muted fw-semibold">Events</small>
                </div>
                <ListGroup variant="flush">
                  {suggestions.map((event) => (
                    <ListGroup.Item
                      key={event._id}
                      action
                      onClick={() => handleSuggestionClick(event)}
                      className="d-flex align-items-center"
                    >
                      <div className="me-3">
                        <Badge bg="secondary">{event.category}</Badge>
                      </div>
                      <div className="flex-grow-1">
                        <div className="fw-semibold">{event.title}</div>
                        <small className="text-muted">{event.venue}</small>
                      </div>
                      <i className="fas fa-arrow-up-right-from-square text-muted"></i>
                    </ListGroup.Item>
                  ))}
                </ListGroup>
              </>
            )}

            {/* Recent Searches */}
            {recentSearches.length > 0 && searchTerm.length === 0 && (
              <>
                <div className="px-3 py-2 bg-light border-bottom">
                  <small className="text-muted fw-semibold">Recent Searches</small>
                </div>
                <ListGroup variant="flush">
                  {recentSearches.map((term, index) => (
                    <ListGroup.Item
                      key={index}
                      action
                      onClick={() => handleRecentSearchClick(term)}
                      className="d-flex align-items-center"
                    >
                      <i className="fas fa-clock-rotate-left me-3 text-muted"></i>
                      <span className="flex-grow-1">{term}</span>
                      <i className="fas fa-arrow-up-right-from-square text-muted"></i>
                    </ListGroup.Item>
                  ))}
                </ListGroup>
              </>
            )}

            {/* No Results */}
            {searchTerm.length > 0 && suggestions.length === 0 && (
              <div className="px-3 py-4 text-center text-muted">
                <i className="fas fa-search mb-2"></i>
                <div>No events found for "{searchTerm}"</div>
                <small>Try different keywords or check filters</small>
              </div>
            )}
          </Card.Body>
        </Card>
      )}

      {/* Advanced Filters */}
      {showAdvanced && (
        <Card className="mb-3">
          <Card.Header className="d-flex justify-content-between align-items-center">
            <h6 className="mb-0">
              <i className="fas fa-filter me-2"></i>
              Advanced Filters
            </h6>
            <Button
              variant="link"
              size="sm"
              onClick={clearFilters}
              className="text-decoration-none p-0"
            >
              Clear All
            </Button>
          </Card.Header>
          <Card.Body>
            <div className="row g-3">
              <div className="col-md-6 col-lg-4">
                <Form.Label>Category</Form.Label>
                <Form.Select
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </Form.Select>
              </div>
              
              <div className="col-md-6 col-lg-4">
                <Form.Label>Date Range</Form.Label>
                <Form.Select
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                >
                  {dateRanges.map(range => (
                    <option key={range} value={range}>{range}</option>
                  ))}
                </Form.Select>
              </div>
              
              <div className="col-md-6 col-lg-4">
                <Form.Label>Venue</Form.Label>
                <Form.Select
                  value={filters.venue}
                  onChange={(e) => handleFilterChange('venue', e.target.value)}
                >
                  {venues.map(venue => (
                    <option key={venue} value={venue}>{venue}</option>
                  ))}
                </Form.Select>
              </div>
              
              <div className="col-md-6 col-lg-4">
                <Form.Label>Registration</Form.Label>
                <Form.Select
                  value={filters.registrationStatus}
                  onChange={(e) => handleFilterChange('registrationStatus', e.target.value)}
                >
                  {registrationStatuses.map(status => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </Form.Select>
              </div>
              
              <div className="col-md-6 col-lg-4">
                <Form.Label>Difficulty</Form.Label>
                <Form.Select
                  value={filters.difficulty}
                  onChange={(e) => handleFilterChange('difficulty', e.target.value)}
                >
                  {difficulties.map(difficulty => (
                    <option key={difficulty} value={difficulty}>{difficulty}</option>
                  ))}
                </Form.Select>
              </div>
            </div>
          </Card.Body>
        </Card>
      )}
    </div>
  );
};

export default AdvancedSearch;
