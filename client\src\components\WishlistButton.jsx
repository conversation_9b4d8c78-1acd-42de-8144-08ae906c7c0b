import React, { useState, useEffect } from 'react';
import { Button, Tooltip, OverlayTrigger } from 'react-bootstrap';
import { useAuth } from '../context/AuthContext';
import { useNotifications } from '../context/NotificationContext';
import { toast } from 'react-toastify';

const WishlistButton = ({ event, size = 'sm', variant = 'outline-warning' }) => {
  const { user, isAuthenticated } = useAuth();
  const { addTemplateNotification } = useNotifications();
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isAuthenticated && user) {
      // Check if event is in user's wishlist
      const wishlist = JSON.parse(localStorage.getItem(`wishlist_${user.id}`) || '[]');
      setIsWishlisted(wishlist.includes(event._id));
    }
  }, [event._id, user, isAuthenticated]);

  const handleWishlistToggle = async () => {
    if (!isAuthenticated) {
      toast.info('Please login to add events to wishlist');
      return;
    }

    setLoading(true);
    
    try {
      const wishlistKey = `wishlist_${user.id}`;
      const currentWishlist = JSON.parse(localStorage.getItem(wishlistKey) || '[]');
      
      let updatedWishlist;
      if (isWishlisted) {
        // Remove from wishlist
        updatedWishlist = currentWishlist.filter(id => id !== event._id);
        toast.success('Removed from wishlist');
        addTemplateNotification('wishlistRemove', event.title);
      } else {
        // Add to wishlist
        updatedWishlist = [...currentWishlist, event._id];
        toast.success('Added to wishlist');
        addTemplateNotification('wishlistAdd', event.title);
      }
      
      localStorage.setItem(wishlistKey, JSON.stringify(updatedWishlist));
      setIsWishlisted(!isWishlisted);
      
      // Dispatch custom event for wishlist updates
      window.dispatchEvent(new CustomEvent('wishlistUpdated', {
        detail: { eventId: event._id, isWishlisted: !isWishlisted }
      }));
      
    } catch (error) {
      console.error('Error updating wishlist:', error);
      toast.error('Failed to update wishlist');
    } finally {
      setLoading(false);
    }
  };

  const tooltip = (
    <Tooltip>
      {isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
    </Tooltip>
  );

  return (
    <OverlayTrigger placement="top" overlay={tooltip}>
      <Button
        variant={isWishlisted ? 'warning' : variant}
        size={size}
        onClick={handleWishlistToggle}
        disabled={loading}
        className={isWishlisted ? 'text-dark' : ''}
      >
        <i className={`fas fa-heart${isWishlisted ? '' : '-o'} ${loading ? 'fa-spin' : ''}`}></i>
        {size !== 'sm' && (
          <span className="ms-1">
            {isWishlisted ? 'Wishlisted' : 'Wishlist'}
          </span>
        )}
      </Button>
    </OverlayTrigger>
  );
};

export default WishlistButton;
