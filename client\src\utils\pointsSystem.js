// Points System for ALTIUS 2K25

export const POINT_VALUES = {
  // Registration points
  EVENT_REGISTRATION: 10,
  FIRST_EVENT_REGISTRATION: 25,
  
  // Participation points
  EVENT_PARTICIPATION: 50,
  EVENT_COMPLETION: 75,
  
  // Achievement points
  PROFILE_COMPLETION: 30,
  FIRST_WISHLIST_ADD: 15,
  MULTIPLE_CATEGORY_PARTICIPATION: 40,
  
  // Winning points
  FIRST_PLACE: 200,
  SECOND_PLACE: 150,
  THIRD_PLACE: 100,
  PARTICIPATION_CERTIFICATE: 25,
  
  // Engagement points
  DAILY_LOGIN: 5,
  WEEKLY_STREAK: 20,
  MONTHLY_STREAK: 50,
  
  // Social points
  REFERRAL: 30,
  FEEDBACK_SUBMISSION: 15,
  
  // Special achievements
  EARLY_BIRD: 20, // Register within first 24 hours
  NIGHT_OWL: 15,  // Register between 10PM-6AM
  WEEKEND_WARRIOR: 10, // Register on weekends
};

export const ACHIEVEMENT_THRESHOLDS = {
  BRONZE: 100,
  SILVER: 500,
  GOLD: 1000,
  PLATINUM: 2500,
  DIAMOND: 5000,
};

export const LEADERBOARD_CATEGORIES = {
  OVERALL: 'overall',
  TECHNICAL: 'technical',
  CULTURAL: 'cultural',
  SPORTS: 'sports',
  LITERARY: 'literary',
  GAMING: 'gaming',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
};

// Calculate points for user actions
export const calculatePoints = (action, data = {}) => {
  switch (action) {
    case 'EVENT_REGISTRATION':
      let points = POINT_VALUES.EVENT_REGISTRATION;
      
      // Bonus for first event
      if (data.isFirstEvent) {
        points += POINT_VALUES.FIRST_EVENT_REGISTRATION;
      }
      
      // Early bird bonus
      if (data.isEarlyBird) {
        points += POINT_VALUES.EARLY_BIRD;
      }
      
      // Time-based bonuses
      const hour = new Date().getHours();
      if (hour >= 22 || hour <= 6) {
        points += POINT_VALUES.NIGHT_OWL;
      }
      
      const day = new Date().getDay();
      if (day === 0 || day === 6) {
        points += POINT_VALUES.WEEKEND_WARRIOR;
      }
      
      return points;
      
    case 'EVENT_PARTICIPATION':
      return POINT_VALUES.EVENT_PARTICIPATION;
      
    case 'EVENT_COMPLETION':
      return POINT_VALUES.EVENT_COMPLETION;
      
    case 'PROFILE_COMPLETION':
      return POINT_VALUES.PROFILE_COMPLETION;
      
    case 'WISHLIST_ADD':
      return data.isFirst ? POINT_VALUES.FIRST_WISHLIST_ADD : 0;
      
    case 'MULTIPLE_CATEGORIES':
      return POINT_VALUES.MULTIPLE_CATEGORY_PARTICIPATION;
      
    case 'WINNING':
      switch (data.position) {
        case 1: return POINT_VALUES.FIRST_PLACE;
        case 2: return POINT_VALUES.SECOND_PLACE;
        case 3: return POINT_VALUES.THIRD_PLACE;
        default: return POINT_VALUES.PARTICIPATION_CERTIFICATE;
      }
      
    case 'DAILY_LOGIN':
      return POINT_VALUES.DAILY_LOGIN;
      
    case 'WEEKLY_STREAK':
      return POINT_VALUES.WEEKLY_STREAK;
      
    case 'MONTHLY_STREAK':
      return POINT_VALUES.MONTHLY_STREAK;
      
    case 'REFERRAL':
      return POINT_VALUES.REFERRAL;
      
    case 'FEEDBACK':
      return POINT_VALUES.FEEDBACK_SUBMISSION;
      
    default:
      return 0;
  }
};

// Get user level based on total points
export const getUserLevel = (totalPoints) => {
  if (totalPoints >= ACHIEVEMENT_THRESHOLDS.DIAMOND) {
    return { level: 'Diamond', color: '#b9f2ff', icon: 'fas fa-gem' };
  } else if (totalPoints >= ACHIEVEMENT_THRESHOLDS.PLATINUM) {
    return { level: 'Platinum', color: '#e5e4e2', icon: 'fas fa-medal' };
  } else if (totalPoints >= ACHIEVEMENT_THRESHOLDS.GOLD) {
    return { level: 'Gold', color: '#ffd700', icon: 'fas fa-trophy' };
  } else if (totalPoints >= ACHIEVEMENT_THRESHOLDS.SILVER) {
    return { level: 'Silver', color: '#c0c0c0', icon: 'fas fa-award' };
  } else if (totalPoints >= ACHIEVEMENT_THRESHOLDS.BRONZE) {
    return { level: 'Bronze', color: '#cd7f32', icon: 'fas fa-star' };
  } else {
    return { level: 'Beginner', color: '#6c757d', icon: 'fas fa-seedling' };
  }
};

// Get progress to next level
export const getProgressToNextLevel = (totalPoints) => {
  const thresholds = Object.values(ACHIEVEMENT_THRESHOLDS).sort((a, b) => a - b);
  const nextThreshold = thresholds.find(threshold => threshold > totalPoints);
  
  if (!nextThreshold) {
    return { progress: 100, pointsToNext: 0, nextLevel: 'Max Level' };
  }
  
  const previousThreshold = thresholds[thresholds.indexOf(nextThreshold) - 1] || 0;
  const progress = ((totalPoints - previousThreshold) / (nextThreshold - previousThreshold)) * 100;
  const pointsToNext = nextThreshold - totalPoints;
  
  const nextLevelName = Object.keys(ACHIEVEMENT_THRESHOLDS).find(
    key => ACHIEVEMENT_THRESHOLDS[key] === nextThreshold
  );
  
  return {
    progress: Math.round(progress),
    pointsToNext,
    nextLevel: nextLevelName
  };
};

// Generate leaderboard data
export const generateLeaderboard = (users, category = 'overall', timeframe = 'all') => {
  let filteredUsers = [...users];
  
  // Filter by timeframe
  if (timeframe === 'weekly') {
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    filteredUsers = users.map(user => ({
      ...user,
      points: user.weeklyPoints || 0
    }));
  } else if (timeframe === 'monthly') {
    const monthAgo = new Date();
    monthAgo.setMonth(monthAgo.getMonth() - 1);
    filteredUsers = users.map(user => ({
      ...user,
      points: user.monthlyPoints || 0
    }));
  }
  
  // Filter by category
  if (category !== 'overall') {
    filteredUsers = users.map(user => ({
      ...user,
      points: user.categoryPoints?.[category] || 0
    }));
  }
  
  // Sort by points
  filteredUsers.sort((a, b) => (b.points || 0) - (a.points || 0));
  
  // Add rankings
  return filteredUsers.map((user, index) => ({
    ...user,
    rank: index + 1,
    level: getUserLevel(user.points || 0)
  }));
};

// Get user's rank in leaderboard
export const getUserRank = (userId, leaderboard) => {
  const userEntry = leaderboard.find(entry => entry._id === userId);
  return userEntry ? userEntry.rank : null;
};

// Calculate category-wise points
export const calculateCategoryPoints = (userEvents) => {
  const categoryPoints = {};
  
  userEvents.forEach(event => {
    const category = event.category.toLowerCase();
    if (!categoryPoints[category]) {
      categoryPoints[category] = 0;
    }
    
    // Base participation points
    categoryPoints[category] += POINT_VALUES.EVENT_PARTICIPATION;
    
    // Completion bonus if event is completed
    if (event.completed) {
      categoryPoints[category] += POINT_VALUES.EVENT_COMPLETION;
    }
    
    // Winning bonus if user won
    if (event.position) {
      categoryPoints[category] += calculatePoints('WINNING', { position: event.position });
    }
  });
  
  return categoryPoints;
};

// Get achievements based on user data
export const getUserAchievements = (userData) => {
  const achievements = [];
  const { totalPoints, registeredEvents, categoryPoints, profileCompleteness } = userData;
  
  // Level-based achievements
  const level = getUserLevel(totalPoints);
  achievements.push({
    id: 'level',
    title: `${level.level} Member`,
    description: `Reached ${level.level} level`,
    icon: level.icon,
    color: level.color,
    earned: true,
    points: totalPoints
  });
  
  // Event-based achievements
  if (registeredEvents >= 1) {
    achievements.push({
      id: 'first_event',
      title: 'First Step',
      description: 'Registered for your first event',
      icon: 'fas fa-baby',
      color: '#28a745',
      earned: true
    });
  }
  
  if (registeredEvents >= 5) {
    achievements.push({
      id: 'event_explorer',
      title: 'Event Explorer',
      description: 'Registered for 5 events',
      icon: 'fas fa-compass',
      color: '#17a2b8',
      earned: true
    });
  }
  
  if (registeredEvents >= 10) {
    achievements.push({
      id: 'super_participant',
      title: 'Super Participant',
      description: 'Registered for 10 events',
      icon: 'fas fa-star',
      color: '#ffc107',
      earned: true
    });
  }
  
  // Category-based achievements
  const categoriesParticipated = Object.keys(categoryPoints || {}).length;
  if (categoriesParticipated >= 3) {
    achievements.push({
      id: 'versatile',
      title: 'Versatile',
      description: 'Participated in 3 different categories',
      icon: 'fas fa-palette',
      color: '#6f42c1',
      earned: true
    });
  }
  
  // Profile completion achievement
  if (profileCompleteness >= 100) {
    achievements.push({
      id: 'profile_complete',
      title: 'Profile Master',
      description: 'Completed your profile 100%',
      icon: 'fas fa-user-check',
      color: '#fd7e14',
      earned: true
    });
  }
  
  return achievements;
};

// Local storage helpers for points
export const saveUserPoints = (userId, points) => {
  const key = `user_points_${userId}`;
  const data = {
    totalPoints: points,
    lastUpdated: new Date().toISOString()
  };
  localStorage.setItem(key, JSON.stringify(data));
};

export const getUserPoints = (userId) => {
  const key = `user_points_${userId}`;
  const data = localStorage.getItem(key);
  if (data) {
    const parsed = JSON.parse(data);
    return parsed.totalPoints || 0;
  }
  return 0;
};

export const addUserPoints = (userId, points, action) => {
  const currentPoints = getUserPoints(userId);
  const newTotal = currentPoints + points;
  saveUserPoints(userId, newTotal);
  
  // Log the action
  const actionsKey = `point_actions_${userId}`;
  const actions = JSON.parse(localStorage.getItem(actionsKey) || '[]');
  actions.unshift({
    action,
    points,
    timestamp: new Date().toISOString(),
    newTotal
  });
  
  // Keep only last 50 actions
  localStorage.setItem(actionsKey, JSON.stringify(actions.slice(0, 50)));
  
  return newTotal;
};
