import React, { useState, useEffect } from 'react';
import { Card, Badge, Button, Modal, Row, Col } from 'react-bootstrap';
import { getCategoryColor, formatDate } from '../utils/helpers';
import EventCard from './EventCard';

const EventCalendar = ({ events, onEventSelect }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showEventModal, setShowEventModal] = useState(false);

  // Get calendar data
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const today = new Date();

  // Get first day of month and number of days
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const daysInMonth = lastDay.getDate();
  const startingDayOfWeek = firstDay.getDay();

  // Month names
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Day names
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  // Group events by date
  const eventsByDate = {};
  events.forEach(event => {
    const eventDate = new Date(event.eventDate);
    const dateKey = `${eventDate.getFullYear()}-${eventDate.getMonth()}-${eventDate.getDate()}`;
    if (!eventsByDate[dateKey]) {
      eventsByDate[dateKey] = [];
    }
    eventsByDate[dateKey].push(event);
  });

  // Navigation functions
  const goToPreviousMonth = () => {
    setCurrentDate(new Date(year, month - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(new Date(year, month + 1, 1));
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  // Handle event click
  const handleEventClick = (event) => {
    setSelectedEvent(event);
    setShowEventModal(true);
    if (onEventSelect) {
      onEventSelect(event);
    }
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(
        <div key={`empty-${i}`} className="calendar-day empty">
        </div>
      );
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const dateKey = `${year}-${month}-${day}`;
      const dayEvents = eventsByDate[dateKey] || [];
      const isToday = date.toDateString() === today.toDateString();
      const isPast = date < today && !isToday;

      days.push(
        <div
          key={day}
          className={`calendar-day ${isToday ? 'today' : ''} ${isPast ? 'past' : ''} ${dayEvents.length > 0 ? 'has-events' : ''}`}
        >
          <div className="day-number">{day}</div>
          <div className="day-events">
            {dayEvents.slice(0, 3).map((event, index) => (
              <div
                key={event._id}
                className={`event-indicator bg-${getCategoryColor(event.category)}`}
                title={event.title}
                onClick={() => handleEventClick(event)}
              >
                <small className="text-white fw-semibold">
                  {event.title.length > 15 ? event.title.substring(0, 15) + '...' : event.title}
                </small>
              </div>
            ))}
            {dayEvents.length > 3 && (
              <div className="more-events">
                <small className="text-muted">+{dayEvents.length - 3} more</small>
              </div>
            )}
          </div>
        </div>
      );
    }

    return days;
  };

  return (
    <>
      <Card className="calendar-card shadow-sm">
        <Card.Header className="bg-light">
          <Row className="align-items-center">
            <Col>
              <h5 className="mb-0 fw-semibold">
                <i className="fas fa-calendar-alt me-2 text-primary"></i>
                Event Calendar
              </h5>
            </Col>
            <Col xs="auto">
              <div className="d-flex gap-2">
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={goToPreviousMonth}
                >
                  <i className="fas fa-chevron-left"></i>
                </Button>
                <Button
                  variant="outline-primary"
                  size="sm"
                  onClick={goToToday}
                  className="px-3"
                >
                  Today
                </Button>
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={goToNextMonth}
                >
                  <i className="fas fa-chevron-right"></i>
                </Button>
              </div>
            </Col>
          </Row>
        </Card.Header>
        <Card.Body className="p-0">
          <div className="calendar-header">
            <h4 className="text-center py-3 mb-0 fw-bold">
              {monthNames[month]} {year}
            </h4>
          </div>
          
          <div className="calendar-grid">
            {/* Day headers */}
            <div className="calendar-day-headers">
              {dayNames.map(day => (
                <div key={day} className="day-header">
                  <small className="fw-semibold text-muted">{day}</small>
                </div>
              ))}
            </div>
            
            {/* Calendar days */}
            <div className="calendar-days">
              {generateCalendarDays()}
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Event Detail Modal */}
      <Modal show={showEventModal} onHide={() => setShowEventModal(false)} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title>Event Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedEvent && (
            <EventCard 
              event={selectedEvent} 
              onRegistrationChange={() => {}} 
              showFullDetails={true}
            />
          )}
        </Modal.Body>
      </Modal>
    </>
  );
};

export default EventCalendar;
