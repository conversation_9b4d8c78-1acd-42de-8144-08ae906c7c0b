import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Form, Button, InputGroup, Pagination } from 'react-bootstrap';
import { useSearchParams } from 'react-router-dom';
import { eventsAPI } from '../services/api';
import EventCard from '../components/EventCard';
import AdvancedSearch from '../components/AdvancedSearch';
import Loading from '../components/Loading';
import { debounce } from '../utils/helpers';

const Events = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    category: 'All',
    search: '',
    page: 1
  });
  const [pagination, setPagination] = useState({
    totalPages: 1,
    currentPage: 1,
    total: 0
  });

  const categories = ['All', 'Technical', 'Cultural', 'Sports', 'Literary', 'Gaming', 'Other'];

  useEffect(() => {
    fetchEvents();
  }, [filters.category, filters.page]);

  // Debounced search
  useEffect(() => {
    const debouncedSearch = debounce(() => {
      if (filters.search !== '') {
        fetchEvents();
      }
    }, 500);

    if (filters.search) {
      debouncedSearch();
    } else {
      fetchEvents();
    }
  }, [filters.search]);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const params = {
        page: filters.page,
        limit: 9
      };

      if (filters.category !== 'All') {
        params.category = filters.category;
      }

      if (filters.search) {
        params.search = filters.search;
      }

      const response = await eventsAPI.getAllEvents(params);
      setEvents(response.data.events);
      setPagination({
        totalPages: response.data.totalPages,
        currentPage: response.data.currentPage,
        total: response.data.total
      });
    } catch (error) {
      console.error('Error fetching events:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value
    }));
  };

  const handlePageChange = (page) => {
    handleFilterChange('page', page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleAdvancedSearch = (searchParams) => {
    setFilters(prev => ({
      ...prev,
      search: searchParams.term,
      category: searchParams.category,
      page: 1
    }));
  };

  const renderPagination = () => {
    if (pagination.totalPages <= 1) return null;

    const items = [];
    const maxVisible = 5;
    const current = pagination.currentPage;
    const total = pagination.totalPages;

    // Previous button
    items.push(
      <Pagination.Prev
        key="prev"
        disabled={current === 1}
        onClick={() => handlePageChange(current - 1)}
      />
    );

    // Page numbers
    let start = Math.max(1, current - Math.floor(maxVisible / 2));
    let end = Math.min(total, start + maxVisible - 1);

    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }

    for (let page = start; page <= end; page++) {
      items.push(
        <Pagination.Item
          key={page}
          active={page === current}
          onClick={() => handlePageChange(page)}
        >
          {page}
        </Pagination.Item>
      );
    }

    // Next button
    items.push(
      <Pagination.Next
        key="next"
        disabled={current === total}
        onClick={() => handlePageChange(current + 1)}
      />
    );

    return (
      <div className="d-flex justify-content-center mt-5">
        <Pagination>{items}</Pagination>
      </div>
    );
  };

  return (
    <div className="events-page py-3 py-md-5">
      <Container fluid className="px-3 px-md-4">
        {/* Header */}
        <Row className="mb-5">
          <Col className="text-center">
            <h1 className="display-4 fw-bold text-dark mb-3">All Events</h1>
            <p className="lead text-muted">
              Discover and register for exciting events at ALTIUS 2K25
            </p>
          </Col>
        </Row>

        {/* Advanced Search */}
        <Row className="mb-4">
          <Col>
            <AdvancedSearch
              events={events}
              onSearch={handleAdvancedSearch}
              placeholder="Search events by title, description, category, or venue..."
            />
          </Col>
        </Row>

        {/* Results Info */}
        <Row className="mb-4">
          <Col>
            <div className="d-flex justify-content-between align-items-center">
              <p className="text-muted mb-0">
                {loading ? 'Loading...' : `Showing ${events.length} of ${pagination.total} events`}
              </p>
              {filters.search && (
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={() => handleFilterChange('search', '')}
                >
                  <i className="fas fa-times me-1"></i>
                  Clear Search
                </Button>
              )}
            </div>
          </Col>
        </Row>

        {/* Events Grid */}
        {loading ? (
          <Loading text="Loading events..." />
        ) : events.length > 0 ? (
          <>
            <Row>
              {events.map((event) => (
                <Col lg={4} md={6} className="mb-4" key={event._id}>
                  <EventCard event={event} onRegistrationChange={fetchEvents} />
                </Col>
              ))}
            </Row>
            {renderPagination()}
          </>
        ) : (
          <Row>
            <Col className="text-center py-5">
              <i className="fas fa-search display-1 text-muted mb-3"></i>
              <h4 className="text-muted">No events found</h4>
              <p className="text-muted">
                {filters.search
                  ? `No events match your search "${filters.search}"`
                  : 'No events available in this category'
                }
              </p>
              {(filters.search || filters.category !== 'All') && (
                <Button
                  variant="outline-primary"
                  onClick={() => {
                    setFilters({ category: 'All', search: '', page: 1 });
                  }}
                >
                  View All Events
                </Button>
              )}
            </Col>
          </Row>
        )}
      </Container>
    </div>
  );
};

export default Events;
