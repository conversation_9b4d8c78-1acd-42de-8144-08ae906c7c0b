// Service Worker for ALTIUS 2K25 PWA
const CACHE_NAME = 'altius-2k25-v1';
const STATIC_CACHE_NAME = 'altius-static-v1';
const DYNAMIC_CACHE_NAME = 'altius-dynamic-v1';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json',
  '/favicon.ico',
  // Add other critical assets
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
  /\/api\/events/,
  /\/api\/auth\/me/,
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .catch((error) => {
        console.error('Service Worker: Error caching static assets', error);
      })
  );
  
  // Force the waiting service worker to become the active service worker
  self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== STATIC_CACHE_NAME && cacheName !== DYNAMIC_CACHE_NAME) {
            console.log('Service Worker: Deleting old cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  
  // Ensure the service worker takes control immediately
  self.clients.claim();
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Handle different types of requests
  if (request.url.includes('/api/')) {
    // API requests - Network First strategy
    event.respondWith(networkFirstStrategy(request));
  } else if (request.destination === 'image') {
    // Images - Cache First strategy
    event.respondWith(cacheFirstStrategy(request));
  } else {
    // Other requests - Stale While Revalidate strategy
    event.respondWith(staleWhileRevalidateStrategy(request));
  }
});

// Network First Strategy (for API calls)
async function networkFirstStrategy(request) {
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    // If successful, cache the response
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    // Network failed, try cache
    console.log('Service Worker: Network failed, trying cache for', request.url);
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline fallback for API requests
    if (request.url.includes('/api/events')) {
      return new Response(JSON.stringify({
        success: false,
        message: 'You are offline. Please check your internet connection.',
        offline: true,
        data: { events: [] }
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    throw error;
  }
}

// Cache First Strategy (for images)
async function cacheFirstStrategy(request) {
  const cachedResponse = await caches.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    // Return placeholder image for offline
    return new Response('', {
      status: 200,
      headers: { 'Content-Type': 'image/svg+xml' }
    });
  }
}

// Stale While Revalidate Strategy (for pages)
async function staleWhileRevalidateStrategy(request) {
  const cache = await caches.open(DYNAMIC_CACHE_NAME);
  const cachedResponse = await cache.match(request);
  
  // Fetch from network in background
  const networkResponsePromise = fetch(request).then((networkResponse) => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch(() => {
    // Network failed, but we might have cache
    return null;
  });
  
  // Return cached version immediately if available
  if (cachedResponse) {
    return cachedResponse;
  }
  
  // Wait for network if no cache
  return networkResponsePromise || new Response('Offline', {
    status: 503,
    statusText: 'Service Unavailable'
  });
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered', event.tag);
  
  if (event.tag === 'event-registration') {
    event.waitUntil(syncEventRegistrations());
  }
  
  if (event.tag === 'wishlist-sync') {
    event.waitUntil(syncWishlistActions());
  }
});

// Sync offline event registrations
async function syncEventRegistrations() {
  try {
    const registrations = await getStoredRegistrations();
    
    for (const registration of registrations) {
      try {
        const response = await fetch('/api/events/' + registration.eventId + '/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + registration.token
          },
          body: JSON.stringify(registration.data)
        });
        
        if (response.ok) {
          await removeStoredRegistration(registration.id);
          console.log('Service Worker: Synced registration for event', registration.eventId);
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync registration', error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Error in syncEventRegistrations', error);
  }
}

// Sync offline wishlist actions
async function syncWishlistActions() {
  try {
    const actions = await getStoredWishlistActions();
    
    for (const action of actions) {
      try {
        // Process wishlist actions when back online
        console.log('Service Worker: Processing wishlist action', action);
        await removeStoredWishlistAction(action.id);
      } catch (error) {
        console.error('Service Worker: Failed to sync wishlist action', error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Error in syncWishlistActions', error);
  }
}

// Helper functions for IndexedDB operations
async function getStoredRegistrations() {
  // Implementation for getting stored offline registrations
  return [];
}

async function removeStoredRegistration(id) {
  // Implementation for removing synced registration
}

async function getStoredWishlistActions() {
  // Implementation for getting stored offline wishlist actions
  return [];
}

async function removeStoredWishlistAction(id) {
  // Implementation for removing synced wishlist action
}

// Push notification handling
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'New notification from ALTIUS 2K25',
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    vibrate: [200, 100, 200],
    data: {
      url: '/'
    },
    actions: [
      {
        action: 'view',
        title: 'View',
        icon: '/favicon.ico'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/favicon.ico'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('ALTIUS 2K25', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked');
  
  event.notification.close();
  
  if (event.action === 'view') {
    event.waitUntil(
      clients.openWindow(event.notification.data.url || '/')
    );
  }
});
