/* Custom CSS for ALTIUS 2K25 */

/* Global Styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Gradient Backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* Text Gradients */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Hover Effects */
.hover-shadow {
  transition: all 0.3s ease;
}

.hover-shadow:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.hover-warning:hover {
  color: #ffc107 !important;
  transition: color 0.3s ease;
}

/* Event Card Styles */
.event-card {
  transition: all 0.3s ease;
  border: none !important;
}

.event-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 2rem 0;
}

/* Custom Button Styles */
.btn-warning {
  background: linear-gradient(45deg, #ffc107, #ffb300);
  border: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-warning:hover {
  background: linear-gradient(45deg, #ffb300, #ff8f00);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
}

/* Loading Animation */
.spinner-border-warning {
  color: #ffc107;
}

/* Custom Navbar */
.navbar-dark .navbar-nav .nav-link:hover {
  color: #ffc107 !important;
}

.navbar-toggler {
  padding: 0.25rem 0.5rem;
  font-size: 1.1rem;
  border: none !important;
  box-shadow: none !important;
}

.navbar-toggler:focus {
  box-shadow: none !important;
}

.navbar-nav .nav-link {
  transition: color 0.3s ease;
  border-radius: 0.375rem;
  margin: 0.125rem 0;
}

.navbar-nav .nav-link:hover {
  background-color: rgba(255, 193, 7, 0.1);
}

.navbar-brand {
  transition: transform 0.3s ease;
}

.navbar-brand:hover {
  transform: scale(1.05);
}

/* Form Styles */
.form-control:focus {
  border-color: #ffc107;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* Card Hover Effects */
.card {
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

/* Stats Section */
.stats-section .card {
  border: none;
  transition: all 0.3s ease;
}

.stats-section .card:hover {
  transform: translateY(-5px);
}

/* Gallery Styles */
.gallery-image {
  transition: transform 0.3s ease;
}

.gallery-card:hover .gallery-image {
  transform: scale(1.05);
}

/* Footer Styles */
footer a:hover {
  color: #ffc107 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .display-1 {
    font-size: 3rem;
  }

  .display-2 {
    font-size: 2.5rem;
  }

  .display-4 {
    font-size: 2rem;
  }

  .display-5 {
    font-size: 1.75rem;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #ffc107;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #ffb300;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Toast Customization */
.Toastify__toast--success {
  background: linear-gradient(135deg, #28a745, #20c997);
}

/* Footer Enhancements */
.hover-warning {
  transition: color 0.3s ease;
}

.hover-warning:hover {
  color: #ffc107 !important;
  text-decoration: none;
}

footer {
  border-top: 1px solid #495057;
}

footer .text-warning {
  color: #ffc107 !important;
}

footer a:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Page Layout Improvements */
.App {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
}

main {
  flex: 1;
  padding-bottom: 2rem;
  width: 100%;
  max-width: 100vw;
}

/* Responsive ratio adjustments for hero section */
@media (max-aspect-ratio: 4/3) {
  .hero-section {
    min-height: 90vh;
    padding: 1.5rem 0;
  }
}

@media (max-aspect-ratio: 3/4) {
  .hero-section {
    min-height: 85vh;
    padding: 1rem 0;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: auto;
    padding: 3rem 0;
  }
}

@media (max-width: 576px) {
  .hero-section {
    min-height: auto;
    padding: 2rem 0;
  }
}

/* Ensure content doesn't get cut off */
.container, .container-fluid {
  padding-bottom: 2rem;
  max-width: 100%;
  overflow-x: hidden;
}

/* Responsive Footer Adjustments */
@media (max-width: 768px) {
  footer {
    text-align: center;
  }

  footer .d-flex {
    justify-content: center !important;
  }

  footer .text-md-end {
    text-align: center !important;
  }

  footer .text-md-start {
    text-align: center !important;
  }

  /* Mobile spacing adjustments */
  main {
    padding-bottom: 1rem;
  }

  .container, .container-fluid {
    padding-bottom: 1rem;
  }
}

.Toastify__toast--error {
  background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.Toastify__toast--warning {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.Toastify__toast--info {
  background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

/* Additional Responsive Improvements */
/* Prevent horizontal scrolling */
* {
  box-sizing: border-box;
}

html, body {
  overflow-x: hidden;
  width: 100%;
}

/* Responsive images */
img {
  max-width: 100%;
  height: auto;
}

/* Responsive tables */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Form improvements for mobile */
@media (max-width: 576px) {
  .form-control {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .btn {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  .modal-dialog {
    margin: 0.5rem;
    max-width: calc(100% - 1rem);
  }
}

/* Card improvements */
.card {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Navbar improvements */
@media (max-width: 991px) {
  .navbar-collapse {
    max-height: 70vh;
    overflow-y: auto;
  }
}

/* Page-specific responsive improvements */
.login-page, .register-page {
  padding: 1rem 0;
}

@media (max-width: 768px) {
  .login-page .card-body,
  .register-page .card-body {
    padding: 2rem 1.5rem;
  }
}

@media (max-width: 576px) {
  .login-page .card-body,
  .register-page .card-body {
    padding: 1.5rem 1rem;
  }
}

/* Events page improvements */
.events-page {
  padding: 2rem 0;
}

@media (max-width: 768px) {
  .events-page {
    padding: 1rem 0;
  }
}

/* Gallery improvements */
.gallery-image {
  width: 100%;
  height: auto;
  object-fit: cover;
}

/* Admin and Dashboard improvements */
@media (max-width: 768px) {
  .table {
    font-size: 0.875rem;
  }

  .btn-group-vertical .btn {
    margin-bottom: 0.25rem;
  }
}

/* Toast positioning for mobile */
@media (max-width: 576px) {
  .Toastify__toast-container {
    width: 100vw;
    padding: 0;
    left: 0;
    margin: 0;
  }

  .Toastify__toast {
    margin-bottom: 0;
    border-radius: 0;
  }
}

/* Notification Dropdown Styles */
.notification-dropdown {
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
}

.notification-item {
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: #f8f9fa !important;
}

.notification-item:last-child {
  border-bottom: none !important;
}

/* Wishlist heart animation */
.wishlist-heart {
  transition: all 0.3s ease;
}

.wishlist-heart:hover {
  transform: scale(1.1);
}

/* Profile completeness bar */
.profile-completeness {
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
}

/* Achievement styles */
.achievement-item {
  transition: all 0.3s ease;
}

.achievement-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.achievement-earned {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.achievement-locked {
  opacity: 0.6;
  filter: grayscale(50%);
}

/* FAQ accordion improvements */
.accordion-item {
  border: 1px solid #dee2e6;
  margin-bottom: 0.5rem;
  border-radius: 0.375rem !important;
  overflow: hidden;
}

.accordion-button {
  background-color: #f8f9fa;
  border: none;
  padding: 1rem 1.25rem;
}

.accordion-button:not(.collapsed) {
  background-color: #e7f3ff;
  color: #0d6efd;
}

.accordion-button:focus {
  box-shadow: none;
  border-color: transparent;
}

.accordion-body {
  padding: 1.25rem;
  background-color: #fff;
}

/* Calendar specific improvements */
.calendar-card .card-header {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-bottom: 2px solid #dee2e6;
}

/* Hover effects for interactive elements */
.hover-shadow {
  transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.cursor-pointer {
  cursor: pointer;
}

/* Responsive improvements for small screens */
@media (max-width: 576px) {
  .notification-dropdown {
    width: 300px !important;
    margin-right: -50px;
  }

  .calendar-day {
    font-size: 0.75rem;
  }

  .achievement-item {
    padding: 0.5rem !important;
  }
}

/* Event Calendar Styles */
.calendar-card {
  border: none;
  overflow: hidden;
}

.calendar-grid {
  display: flex;
  flex-direction: column;
}

.calendar-day-headers {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.day-header {
  padding: 0.75rem 0.5rem;
  text-align: center;
  border-right: 1px solid #dee2e6;
}

.day-header:last-child {
  border-right: none;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  min-height: 400px;
}

.calendar-day {
  min-height: 80px;
  border-right: 1px solid #dee2e6;
  border-bottom: 1px solid #dee2e6;
  padding: 0.5rem;
  position: relative;
  background-color: #fff;
  transition: background-color 0.2s ease;
}

.calendar-day:last-child {
  border-right: none;
}

.calendar-day.empty {
  background-color: #f8f9fa;
}

.calendar-day.today {
  background-color: #fff3cd;
  border: 2px solid #ffc107;
}

.calendar-day.past {
  background-color: #f8f9fa;
  color: #6c757d;
}

.calendar-day.has-events {
  background-color: #f0f8ff;
}

.calendar-day:hover:not(.empty) {
  background-color: #e3f2fd;
}

.day-number {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  color: #495057;
}

.calendar-day.today .day-number {
  color: #856404;
  font-weight: 700;
}

.calendar-day.past .day-number {
  color: #adb5bd;
}

.day-events {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.event-indicator {
  padding: 2px 4px;
  border-radius: 3px;
  cursor: pointer;
  transition: transform 0.2s ease;
  font-size: 0.7rem;
  line-height: 1.2;
}

.event-indicator:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.more-events {
  text-align: center;
  padding: 2px;
  cursor: pointer;
}

/* Responsive calendar */
@media (max-width: 768px) {
  .calendar-days {
    min-height: 300px;
  }

  .calendar-day {
    min-height: 60px;
    padding: 0.25rem;
  }

  .day-number {
    font-size: 0.75rem;
  }

  .event-indicator {
    font-size: 0.6rem;
    padding: 1px 2px;
  }
}

@media (max-width: 576px) {
  .calendar-days {
    min-height: 250px;
  }

  .calendar-day {
    min-height: 50px;
    padding: 0.125rem;
  }

  .day-number {
    font-size: 0.7rem;
  }

  .event-indicator {
    font-size: 0.55rem;
    padding: 1px;
  }
}
