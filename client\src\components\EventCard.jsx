import React, { useState } from 'react';
import { Card, Button, Badge, Modal } from 'react-bootstrap';
import { useAuth } from '../context/AuthContext';
import { eventsAPI } from '../services/api';
import { formatDate, getCategoryColor, isRegistrationOpen, getTimeRemaining, truncateText } from '../utils/helpers';
import { calculatePoints, addUserPoints } from '../utils/pointsSystem';
import EventRegistrationModal from './EventRegistrationModal';
import WishlistButton from './WishlistButton';
import { useNotifications } from '../context/NotificationContext';
import { toast } from 'react-toastify';

const EventCard = ({ event, onRegistrationChange, compact = false, showFullDetails = false }) => {
  const { user, isAuthenticated } = useAuth();
  const { addTemplateNotification } = useNotifications();
  const [loading, setLoading] = useState(false);
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [showPosterModal, setShowPosterModal] = useState(false);

  const isRegistered = user?.registeredEvents?.some(e => e._id === event._id || e === event._id);
  const canRegister = isRegistrationOpen(event.eventDate) && (event.availableSpots > 0 || event.availableSpots === undefined);

  const handleRegistrationSuccess = () => {
    // Award points for registration
    if (user) {
      const isFirstEvent = !user.registeredEvents || user.registeredEvents.length === 0;
      const points = calculatePoints('EVENT_REGISTRATION', {
        isFirstEvent,
        isEarlyBird: true // You can add logic to check if registration is within first 24 hours
      });

      const newTotal = addUserPoints(user._id, points, 'EVENT_REGISTRATION');

      // Show notification
      addTemplateNotification('eventRegistration', event.title);

      // Show points earned toast
      toast.success(`🎉 Registration successful! You earned ${points} points!`, {
        position: 'top-center',
        autoClose: 3000
      });
    }

    onRegistrationChange && onRegistrationChange();
  };

  const handleRegisterClick = () => {
    if (!isAuthenticated) {
      toast.error('Please login to register for events');
      return;
    }

    setShowRegistrationModal(true);
  };

  const handleUnregister = async () => {
    setLoading(true);
    try {
      await eventsAPI.unregisterFromEvent(event._id);
      toast.success('Successfully unregistered from the event');
      onRegistrationChange && onRegistrationChange();
    } catch (error) {
      const message = error.response?.data?.message || 'Unregistration failed';
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  // Compact version for sidebar
  if (compact) {
    return (
      <>
        <Card className="shadow-sm event-card">
          <Card.Body className="p-3">
            <div className="d-flex align-items-start mb-2">
              <Badge bg={getCategoryColor(event.category)} className="me-2">
                {event.category}
              </Badge>
              {!canRegister && (
                <Badge bg="danger" className="ms-auto">
                  Closed
                </Badge>
              )}
            </div>

            <Card.Title className="fw-bold text-dark mb-2 h6">
              {event.title}
            </Card.Title>

            <div className="mb-2">
              <small className="text-muted d-block">
                <i className="fas fa-calendar me-1"></i>
                {formatDate(event.eventDate)}
              </small>
              <small className="text-muted d-block">
                <i className="fas fa-map-marker-alt me-1"></i>
                {event.venue}
              </small>
            </div>

            <div className="d-flex gap-1">
              {isAuthenticated ? (
                isRegistered ? (
                  <Button
                    variant="outline-danger"
                    size="sm"
                    onClick={handleUnregister}
                    disabled={loading}
                    className="flex-grow-1"
                  >
                    {loading ? 'Unregistering...' : 'Unregister'}
                  </Button>
                ) : (
                  <Button
                    variant="warning"
                    size="sm"
                    onClick={handleRegisterClick}
                    className="flex-grow-1 text-dark fw-semibold"
                  >
                    Register
                  </Button>
                )
              ) : (
                <Button
                  variant="outline-warning"
                  size="sm"
                  onClick={() => toast.info('Please login to register')}
                  className="flex-grow-1"
                >
                  Login to Register
                </Button>
              )}

              <WishlistButton event={event} size="sm" />

              <Button
                variant="outline-primary"
                size="sm"
                onClick={() => setShowPosterModal(true)}
              >
                <i className="fas fa-eye"></i>
              </Button>
            </div>
          </Card.Body>
        </Card>

        {/* Event Registration Modal */}
        <EventRegistrationModal
          show={showRegistrationModal}
          onHide={() => setShowRegistrationModal(false)}
          event={event}
          onSuccess={handleRegistrationSuccess}
        />

        {/* Poster Modal */}
        <Modal show={showPosterModal} onHide={() => setShowPosterModal(false)} size="lg" centered>
          <Modal.Header closeButton>
            <Modal.Title>{event.title}</Modal.Title>
          </Modal.Header>
          <Modal.Body className="text-center">
            <img
              src={`http://localhost:5000${event.posterURL}`}
              alt={event.title}
              className="img-fluid rounded"
              style={{ maxHeight: '70vh' }}
            />
          </Modal.Body>
        </Modal>
      </>
    );
  }

  return (
    <>
      <Card className="h-100 shadow-sm hover-shadow event-card">
        <div className="position-relative">
          <Card.Img
            variant="top"
            src={`http://localhost:5000${event.posterURL}`}
            alt={event.title}
            style={{ height: '200px', objectFit: 'cover', cursor: 'pointer' }}
            onClick={() => setShowPosterModal(true)}
          />
          <Badge
            bg={getCategoryColor(event.category)}
            className="position-absolute top-0 start-0 m-2"
          >
            {event.category}
          </Badge>
          {!canRegister && (
            <Badge
              bg="danger"
              className="position-absolute top-0 end-0 m-2"
            >
              Registration Closed
            </Badge>
          )}
        </div>

        <Card.Body className="d-flex flex-column">
          <Card.Title className="fw-bold text-dark mb-2">
            {event.title}
          </Card.Title>
          
          <Card.Text className="text-muted mb-3 flex-grow-1">
            {truncateText(event.description, 120)}
          </Card.Text>

          <div className="mb-3">
            <small className="text-muted d-block">
              <i className="fas fa-calendar me-1"></i>
              {formatDate(event.eventDate)}
            </small>
            <small className="text-muted d-block">
              <i className="fas fa-map-marker-alt me-1"></i>
              {event.venue}
            </small>
            <small className="text-muted d-block">
              <i className="fas fa-users me-1"></i>
              {event.registrationCount || 0} / {event.maxParticipants} registered
            </small>
            <small className={`d-block ${canRegister ? 'text-success' : 'text-danger'}`}>
              <i className="fas fa-clock me-1"></i>
              {getTimeRemaining(event.eventDate)}
            </small>
          </div>

          <div className="d-flex gap-2">
            {isAuthenticated ? (
              isRegistered ? (
                <Button
                  variant="outline-danger"
                  size="sm"
                  onClick={handleUnregister}
                  disabled={loading}
                  className="flex-grow-1"
                >
                  {loading ? 'Unregistering...' : 'Unregister'}
                </Button>
              ) : (
                <Button
                  variant="warning"
                  size="sm"
                  onClick={handleRegisterClick}
                  className="flex-grow-1 text-dark fw-semibold"
                >
                  Register
                </Button>
              )
            ) : (
              <Button
                variant="outline-warning"
                size="sm"
                onClick={() => toast.info('Please login to register')}
                className="flex-grow-1"
              >
                Login to Register
              </Button>
            )}

            <WishlistButton event={event} size="sm" />

            <Button
              variant="outline-primary"
              size="sm"
              onClick={() => setShowPosterModal(true)}
            >
              <i className="fas fa-eye"></i>
            </Button>
          </div>
        </Card.Body>
      </Card>

      {/* Event Registration Modal */}
      <EventRegistrationModal
        show={showRegistrationModal}
        onHide={() => setShowRegistrationModal(false)}
        event={event}
        onSuccess={handleRegistrationSuccess}
      />

      {/* Poster Modal */}
      <Modal show={showPosterModal} onHide={() => setShowPosterModal(false)} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title>{event.title}</Modal.Title>
        </Modal.Header>
        <Modal.Body className="text-center">
          <img
            src={`http://localhost:5000${event.posterURL}`}
            alt={event.title}
            className="img-fluid rounded"
            style={{ maxHeight: '70vh' }}
          />
        </Modal.Body>
      </Modal>
    </>
  );
};

export default EventCard;
