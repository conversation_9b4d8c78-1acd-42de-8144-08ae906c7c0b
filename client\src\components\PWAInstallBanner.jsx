import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Modal } from 'react-bootstrap';
import { usePWA } from '../hooks/usePWA';

const PWAInstallBanner = () => {
  const { isInstallable, isInstalled, isOnline, installApp, getAppInfo } = usePWA();
  const [showBanner, setShowBanner] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [dismissed, setDismissed] = useState(false);

  useEffect(() => {
    // Check if user has dismissed the banner before
    const isDismissed = localStorage.getItem('pwa_install_dismissed') === 'true';
    setDismissed(isDismissed);

    // Show banner if app is installable and not dismissed
    if (isInstallable && !isInstalled && !isDismissed) {
      // Delay showing banner to avoid interrupting user
      const timer = setTimeout(() => {
        setShowBanner(true);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isInstallable, isInstalled]);

  const handleInstall = async () => {
    const success = await installApp();
    if (success) {
      setShowBanner(false);
      setShowModal(false);
    }
  };

  const handleDismiss = () => {
    setShowBanner(false);
    setDismissed(true);
    localStorage.setItem('pwa_install_dismissed', 'true');
  };

  const handleLearnMore = () => {
    setShowModal(true);
  };

  // Don't show if already installed or dismissed
  if (isInstalled || dismissed || !isInstallable) {
    return null;
  }

  return (
    <>
      {/* Install Banner */}
      {showBanner && (
        <Alert 
          variant="primary" 
          className="position-fixed bottom-0 start-0 end-0 m-3 shadow-lg"
          style={{ zIndex: 1050 }}
          dismissible={false}
        >
          <div className="d-flex align-items-center justify-content-between">
            <div className="d-flex align-items-center">
              <i className="fas fa-mobile-alt me-3 fs-4"></i>
              <div>
                <strong>Install ALTIUS 2K25 App</strong>
                <div className="small">Get the full app experience with offline access!</div>
              </div>
            </div>
            <div className="d-flex gap-2">
              <Button 
                variant="outline-primary" 
                size="sm"
                onClick={handleLearnMore}
              >
                Learn More
              </Button>
              <Button 
                variant="primary" 
                size="sm"
                onClick={handleInstall}
              >
                Install
              </Button>
              <Button 
                variant="link" 
                size="sm"
                className="text-muted"
                onClick={handleDismiss}
              >
                <i className="fas fa-times"></i>
              </Button>
            </div>
          </div>
        </Alert>
      )}

      {/* Install Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-mobile-alt me-2 text-primary"></i>
            Install ALTIUS 2K25 App
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-center mb-4">
            <i className="fas fa-download display-1 text-primary mb-3"></i>
            <h5>Get the Best Experience</h5>
            <p className="text-muted">
              Install our app for faster access, offline functionality, and push notifications.
            </p>
          </div>

          <div className="row g-3">
            <div className="col-md-6">
              <Card className="h-100 border-0 bg-light">
                <Card.Body className="text-center">
                  <i className="fas fa-wifi-slash text-success mb-2"></i>
                  <h6>Offline Access</h6>
                  <small className="text-muted">
                    Browse events and access your data even without internet
                  </small>
                </Card.Body>
              </Card>
            </div>
            <div className="col-md-6">
              <Card className="h-100 border-0 bg-light">
                <Card.Body className="text-center">
                  <i className="fas fa-bell text-warning mb-2"></i>
                  <h6>Push Notifications</h6>
                  <small className="text-muted">
                    Get notified about event updates and reminders
                  </small>
                </Card.Body>
              </Card>
            </div>
            <div className="col-md-6">
              <Card className="h-100 border-0 bg-light">
                <Card.Body className="text-center">
                  <i className="fas fa-rocket text-info mb-2"></i>
                  <h6>Faster Loading</h6>
                  <small className="text-muted">
                    Instant app startup and faster navigation
                  </small>
                </Card.Body>
              </Card>
            </div>
            <div className="col-md-6">
              <Card className="h-100 border-0 bg-light">
                <Card.Body className="text-center">
                  <i className="fas fa-home text-danger mb-2"></i>
                  <h6>Home Screen</h6>
                  <small className="text-muted">
                    Add to home screen for easy access
                  </small>
                </Card.Body>
              </Card>
            </div>
          </div>

          <div className="mt-4">
            <h6>How to Install:</h6>
            <ol className="small text-muted">
              <li>Click the "Install" button below</li>
              <li>Confirm the installation in your browser</li>
              <li>The app will be added to your home screen</li>
              <li>Launch it anytime like a native app!</li>
            </ol>
          </div>

          {!isOnline && (
            <Alert variant="warning" className="mt-3">
              <i className="fas fa-exclamation-triangle me-2"></i>
              You're currently offline. The app will still work, but some features may be limited.
            </Alert>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Maybe Later
          </Button>
          <Button variant="primary" onClick={handleInstall}>
            <i className="fas fa-download me-2"></i>
            Install Now
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default PWAInstallBanner;
