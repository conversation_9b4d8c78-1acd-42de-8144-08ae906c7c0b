import React, { useState, useEffect } from 'react';
import { Card, Table, Badge, Button, Form, Row, Col, ProgressBar, Avatar } from 'react-bootstrap';
import { useAuth } from '../context/AuthContext';
import { getUserLevel, generateLeaderboard, getUserRank } from '../utils/pointsSystem';

const Leaderboard = ({ users = [], category = 'overall', timeframe = 'all' }) => {
  const { user } = useAuth();
  const [leaderboardData, setLeaderboardData] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(category);
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe);
  const [showTop, setShowTop] = useState(10);

  const categories = [
    { value: 'overall', label: 'Overall', icon: 'fas fa-trophy' },
    { value: 'technical', label: 'Technical', icon: 'fas fa-code' },
    { value: 'cultural', label: 'Cultural', icon: 'fas fa-music' },
    { value: 'sports', label: 'Sports', icon: 'fas fa-running' },
    { value: 'literary', label: 'Literary', icon: 'fas fa-book' },
    { value: 'gaming', label: 'Gaming', icon: 'fas fa-gamepad' }
  ];

  const timeframes = [
    { value: 'all', label: 'All Time' },
    { value: 'monthly', label: 'This Month' },
    { value: 'weekly', label: 'This Week' }
  ];

  useEffect(() => {
    const leaderboard = generateLeaderboard(users, selectedCategory, selectedTimeframe);
    setLeaderboardData(leaderboard);
  }, [users, selectedCategory, selectedTimeframe]);

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return <i className="fas fa-crown text-warning fs-4"></i>;
      case 2:
        return <i className="fas fa-medal text-secondary fs-5"></i>;
      case 3:
        return <i className="fas fa-award text-warning fs-5"></i>;
      default:
        return <span className="fw-bold text-muted">#{rank}</span>;
    }
  };

  const getRankBadge = (rank) => {
    if (rank === 1) return 'warning';
    if (rank === 2) return 'secondary';
    if (rank === 3) return 'success';
    return 'light';
  };

  const userRank = getUserRank(user?._id, leaderboardData);
  const topUsers = leaderboardData.slice(0, showTop);

  return (
    <Card className="leaderboard-card shadow-sm">
      <Card.Header className="bg-gradient-primary text-white">
        <Row className="align-items-center">
          <Col>
            <h5 className="mb-0 fw-semibold">
              <i className="fas fa-trophy me-2"></i>
              Leaderboard
            </h5>
          </Col>
          <Col xs="auto">
            {user && userRank && (
              <Badge bg="warning" className="text-dark">
                Your Rank: #{userRank}
              </Badge>
            )}
          </Col>
        </Row>
      </Card.Header>

      <Card.Body className="p-0">
        {/* Filters */}
        <div className="p-3 border-bottom bg-light">
          <Row className="g-3">
            <Col md={6}>
              <Form.Label className="small fw-semibold text-muted">Category</Form.Label>
              <Form.Select
                size="sm"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                {categories.map(cat => (
                  <option key={cat.value} value={cat.value}>
                    {cat.label}
                  </option>
                ))}
              </Form.Select>
            </Col>
            <Col md={6}>
              <Form.Label className="small fw-semibold text-muted">Timeframe</Form.Label>
              <Form.Select
                size="sm"
                value={selectedTimeframe}
                onChange={(e) => setSelectedTimeframe(e.target.value)}
              >
                {timeframes.map(tf => (
                  <option key={tf.value} value={tf.value}>
                    {tf.label}
                  </option>
                ))}
              </Form.Select>
            </Col>
          </Row>
        </div>

        {/* Top 3 Podium */}
        {topUsers.length >= 3 && (
          <div className="p-4 bg-light border-bottom">
            <Row className="text-center">
              {/* Second Place */}
              <Col xs={4} className="d-flex flex-column align-items-center">
                <div className="position-relative mb-2">
                  <div 
                    className="rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white fw-bold"
                    style={{ width: '60px', height: '60px' }}
                  >
                    {topUsers[1]?.name?.charAt(0) || 'U'}
                  </div>
                  <Badge 
                    bg="secondary" 
                    className="position-absolute bottom-0 start-100 translate-middle"
                  >
                    2
                  </Badge>
                </div>
                <div className="small fw-semibold">{topUsers[1]?.name}</div>
                <div className="small text-muted">{topUsers[1]?.points || 0} pts</div>
              </Col>

              {/* First Place */}
              <Col xs={4} className="d-flex flex-column align-items-center">
                <div className="position-relative mb-2">
                  <div 
                    className="rounded-circle bg-warning d-flex align-items-center justify-content-center text-dark fw-bold"
                    style={{ width: '80px', height: '80px' }}
                  >
                    {topUsers[0]?.name?.charAt(0) || 'U'}
                  </div>
                  <i className="fas fa-crown position-absolute top-0 start-50 translate-middle text-warning fs-4"></i>
                  <Badge 
                    bg="warning" 
                    className="position-absolute bottom-0 start-100 translate-middle text-dark"
                  >
                    1
                  </Badge>
                </div>
                <div className="fw-bold">{topUsers[0]?.name}</div>
                <div className="small text-muted">{topUsers[0]?.points || 0} pts</div>
                <Badge bg="warning" className="text-dark small">
                  {topUsers[0]?.level?.level}
                </Badge>
              </Col>

              {/* Third Place */}
              <Col xs={4} className="d-flex flex-column align-items-center">
                <div className="position-relative mb-2">
                  <div 
                    className="rounded-circle bg-success d-flex align-items-center justify-content-center text-white fw-bold"
                    style={{ width: '60px', height: '60px' }}
                  >
                    {topUsers[2]?.name?.charAt(0) || 'U'}
                  </div>
                  <Badge 
                    bg="success" 
                    className="position-absolute bottom-0 start-100 translate-middle"
                  >
                    3
                  </Badge>
                </div>
                <div className="small fw-semibold">{topUsers[2]?.name}</div>
                <div className="small text-muted">{topUsers[2]?.points || 0} pts</div>
              </Col>
            </Row>
          </div>
        )}

        {/* Leaderboard Table */}
        <div className="table-responsive">
          <Table className="mb-0" hover>
            <thead className="bg-light">
              <tr>
                <th className="border-0 py-3 ps-3">Rank</th>
                <th className="border-0 py-3">User</th>
                <th className="border-0 py-3">Level</th>
                <th className="border-0 py-3 text-end pe-3">Points</th>
              </tr>
            </thead>
            <tbody>
              {topUsers.map((userData, index) => (
                <tr 
                  key={userData._id} 
                  className={`${userData._id === user?._id ? 'table-warning' : ''}`}
                >
                  <td className="ps-3 py-3">
                    <div className="d-flex align-items-center">
                      {getRankIcon(userData.rank)}
                    </div>
                  </td>
                  <td className="py-3">
                    <div className="d-flex align-items-center">
                      <div 
                        className="rounded-circle bg-primary d-flex align-items-center justify-content-center text-white fw-bold me-3"
                        style={{ width: '40px', height: '40px', fontSize: '0.875rem' }}
                      >
                        {userData.name?.charAt(0) || 'U'}
                      </div>
                      <div>
                        <div className="fw-semibold">
                          {userData.name}
                          {userData._id === user?._id && (
                            <Badge bg="info" className="ms-2 small">You</Badge>
                          )}
                        </div>
                        <div className="small text-muted">{userData.department}</div>
                      </div>
                    </div>
                  </td>
                  <td className="py-3">
                    <Badge 
                      style={{ backgroundColor: userData.level?.color }}
                      className="text-dark"
                    >
                      <i className={`${userData.level?.icon} me-1`}></i>
                      {userData.level?.level}
                    </Badge>
                  </td>
                  <td className="py-3 text-end pe-3">
                    <div className="fw-bold text-primary">
                      {userData.points || 0}
                    </div>
                    <div className="small text-muted">points</div>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </div>

        {/* Load More */}
        {leaderboardData.length > showTop && (
          <div className="p-3 text-center border-top">
            <Button
              variant="outline-primary"
              size="sm"
              onClick={() => setShowTop(prev => prev + 10)}
            >
              Show More ({leaderboardData.length - showTop} remaining)
            </Button>
          </div>
        )}

        {/* Empty State */}
        {leaderboardData.length === 0 && (
          <div className="p-5 text-center">
            <i className="fas fa-trophy display-1 text-muted mb-3"></i>
            <h5 className="text-muted">No Data Available</h5>
            <p className="text-muted">
              Leaderboard will appear once users start earning points.
            </p>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default Leaderboard;
