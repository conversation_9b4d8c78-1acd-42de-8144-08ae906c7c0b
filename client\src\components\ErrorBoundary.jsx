import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, Button, Alert } from 'react-bootstrap';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to console for debugging
    console.error('Error caught by boundary:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <Container className="py-5">
          <Row className="justify-content-center">
            <Col md={8} lg={6}>
              <div className="text-center mb-4">
                <i className="fas fa-exclamation-triangle display-1 text-warning mb-3"></i>
                <h2 className="text-dark mb-3">Oops! Something went wrong</h2>
                <p className="text-muted mb-4">
                  We're sorry, but something unexpected happened. Please try refreshing the page.
                </p>
              </div>

              <Alert variant="danger" className="mb-4">
                <Alert.Heading>Error Details</Alert.Heading>
                <p className="mb-0">
                  <strong>Error:</strong> {this.state.error && this.state.error.toString()}
                </p>
                {this.state.errorInfo && (
                  <details className="mt-2">
                    <summary>Stack Trace</summary>
                    <pre className="mt-2 small">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </details>
                )}
              </Alert>

              <div className="text-center">
                <Button
                  variant="primary"
                  onClick={() => window.location.reload()}
                  className="me-3"
                >
                  <i className="fas fa-redo me-2"></i>
                  Refresh Page
                </Button>
                <Button
                  variant="outline-secondary"
                  onClick={() => window.location.href = '/'}
                >
                  <i className="fas fa-home me-2"></i>
                  Go Home
                </Button>
              </div>
            </Col>
          </Row>
        </Container>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
