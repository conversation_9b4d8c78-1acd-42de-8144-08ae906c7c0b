import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Badge, Modal, Alert, ProgressBar } from 'react-bootstrap';
import { useAuth } from '../context/AuthContext';
import { authAPI } from '../services/api';
import { formatDate, getCategoryColor } from '../utils/helpers';
import { toast } from 'react-toastify';

const Profile = () => {
  const { user, dispatch } = useAuth();
  const [loading, setLoading] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    usn: user?.usn || '',
    department: user?.department || '',
    year: user?.year || '',
    bio: user?.bio || '',
    interests: user?.interests || []
  });
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [registeredEvents, setRegisteredEvents] = useState([]);
  const [achievements, setAchievements] = useState([]);

  const departments = [
    'Computer Science', 'Information Science', 'Electronics & Communication',
    'Mechanical Engineering', 'Civil Engineering', 'Electrical Engineering',
    'Chemical Engineering', 'Biotechnology', 'Other'
  ];

  const years = ['1st Year', '2nd Year', '3rd Year', '4th Year', 'Graduate'];

  const interestOptions = [
    'Technical', 'Cultural', 'Sports', 'Literary', 'Gaming', 'Photography',
    'Music', 'Dance', 'Drama', 'Coding', 'Design', 'Entrepreneurship'
  ];

  useEffect(() => {
    fetchUserData();
    calculateAchievements();
  }, []);

  const fetchUserData = async () => {
    try {
      const response = await authAPI.getProfile();
      const userData = response.data.user;
      setRegisteredEvents(userData.registeredEvents || []);
      setProfileData({
        name: userData.name || '',
        email: userData.email || '',
        usn: userData.usn || '',
        department: userData.department || '',
        year: userData.year || '',
        bio: userData.bio || '',
        interests: userData.interests || []
      });
    } catch (error) {
      console.error('Error fetching user data:', error);
      toast.error('Failed to load profile data');
    }
  };

  const calculateAchievements = () => {
    const userAchievements = [];
    
    // Registration-based achievements
    if (registeredEvents.length >= 1) {
      userAchievements.push({
        title: 'First Step',
        description: 'Registered for your first event',
        icon: 'fas fa-baby',
        color: 'success',
        earned: true
      });
    }
    
    if (registeredEvents.length >= 5) {
      userAchievements.push({
        title: 'Event Explorer',
        description: 'Registered for 5 events',
        icon: 'fas fa-compass',
        color: 'info',
        earned: true
      });
    }
    
    if (registeredEvents.length >= 10) {
      userAchievements.push({
        title: 'Super Participant',
        description: 'Registered for 10 events',
        icon: 'fas fa-star',
        color: 'warning',
        earned: true
      });
    }

    // Category-based achievements
    const categories = new Set(registeredEvents.map(event => event.category));
    if (categories.size >= 3) {
      userAchievements.push({
        title: 'Versatile',
        description: 'Participated in 3 different categories',
        icon: 'fas fa-palette',
        color: 'primary',
        earned: true
      });
    }

    // Add potential achievements
    if (registeredEvents.length < 5) {
      userAchievements.push({
        title: 'Event Explorer',
        description: 'Register for 5 events',
        icon: 'fas fa-compass',
        color: 'secondary',
        earned: false
      });
    }

    setAchievements(userAchievements);
  };

  const handleProfileUpdate = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await authAPI.updateProfile(profileData);
      
      // Update user in context
      dispatch({
        type: 'LOAD_USER_SUCCESS',
        payload: response.data.user
      });
      
      toast.success('Profile updated successfully!');
      setShowEditModal(false);
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to update profile';
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (e) => {
    e.preventDefault();
    
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }
    
    setLoading(true);
    
    try {
      await authAPI.changePassword({
        currentPassword: passwordData.currentPassword,
        newPassword: passwordData.newPassword
      });
      
      toast.success('Password changed successfully!');
      setShowPasswordModal(false);
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to change password';
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  const handleInterestToggle = (interest) => {
    setProfileData(prev => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest]
    }));
  };

  const getProfileCompleteness = () => {
    const fields = ['name', 'email', 'usn', 'department', 'year'];
    const filledFields = fields.filter(field => profileData[field] && profileData[field].trim() !== '');
    return Math.round((filledFields.length / fields.length) * 100);
  };

  const getParticipationStats = () => {
    const now = new Date();
    const upcoming = registeredEvents.filter(event => new Date(event.eventDate) > now);
    const past = registeredEvents.filter(event => new Date(event.eventDate) <= now);
    const categories = new Set(registeredEvents.map(event => event.category));
    
    return {
      total: registeredEvents.length,
      upcoming: upcoming.length,
      past: past.length,
      categories: categories.size
    };
  };

  const stats = getParticipationStats();
  const completeness = getProfileCompleteness();

  return (
    <div className="profile-page py-3 py-md-5">
      <Container fluid className="px-3 px-md-4">
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <h1 className="display-5 fw-bold text-dark mb-2">My Profile</h1>
            <p className="text-muted">Manage your account settings and view your activity</p>
          </Col>
        </Row>

        {/* Profile Completeness Alert */}
        {completeness < 100 && (
          <Row className="mb-4">
            <Col>
              <Alert variant="warning">
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <strong>Complete your profile!</strong> Your profile is {completeness}% complete.
                  </div>
                  <Button
                    variant="warning"
                    size="sm"
                    onClick={() => setShowEditModal(true)}
                  >
                    Complete Now
                  </Button>
                </div>
                <ProgressBar 
                  variant="warning" 
                  now={completeness} 
                  className="mt-2" 
                  style={{ height: '6px' }}
                />
              </Alert>
            </Col>
          </Row>
        )}

        <Row>
          {/* Left Column - Profile Info */}
          <Col lg={8} className="mb-4">
            {/* Basic Info Card */}
            <Card className="mb-4 shadow-sm">
              <Card.Header className="bg-light d-flex justify-content-between align-items-center">
                <h5 className="mb-0 fw-semibold">
                  <i className="fas fa-user me-2 text-primary"></i>
                  Profile Information
                </h5>
                <Button
                  variant="outline-primary"
                  size="sm"
                  onClick={() => setShowEditModal(true)}
                >
                  <i className="fas fa-edit me-1"></i>
                  Edit
                </Button>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6} className="mb-3">
                    <strong>Name:</strong>
                    <div className="text-muted">{profileData.name || 'Not provided'}</div>
                  </Col>
                  <Col md={6} className="mb-3">
                    <strong>Email:</strong>
                    <div className="text-muted">{profileData.email || 'Not provided'}</div>
                  </Col>
                  <Col md={6} className="mb-3">
                    <strong>USN:</strong>
                    <div className="text-muted">{profileData.usn || 'Not provided'}</div>
                  </Col>
                  <Col md={6} className="mb-3">
                    <strong>Department:</strong>
                    <div className="text-muted">{profileData.department || 'Not provided'}</div>
                  </Col>
                  <Col md={6} className="mb-3">
                    <strong>Year:</strong>
                    <div className="text-muted">{profileData.year || 'Not provided'}</div>
                  </Col>
                  <Col md={6} className="mb-3">
                    <strong>Account Type:</strong>
                    <div>
                      <Badge bg={user?.isAdmin ? 'danger' : 'primary'}>
                        {user?.isAdmin ? 'Admin' : 'Student'}
                      </Badge>
                    </div>
                  </Col>
                  <Col md={12} className="mb-3">
                    <strong>Bio:</strong>
                    <div className="text-muted">{profileData.bio || 'No bio provided'}</div>
                  </Col>
                  <Col md={12}>
                    <strong>Interests:</strong>
                    <div className="mt-2">
                      {profileData.interests.length > 0 ? (
                        profileData.interests.map(interest => (
                          <Badge key={interest} bg="secondary" className="me-1 mb-1">
                            {interest}
                          </Badge>
                        ))
                      ) : (
                        <span className="text-muted">No interests specified</span>
                      )}
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Security Settings */}
            <Card className="shadow-sm">
              <Card.Header className="bg-light">
                <h5 className="mb-0 fw-semibold">
                  <i className="fas fa-shield-alt me-2 text-success"></i>
                  Security Settings
                </h5>
              </Card.Header>
              <Card.Body>
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <strong>Password</strong>
                    <div className="text-muted small">Last changed: {formatDate(user?.updatedAt)}</div>
                  </div>
                  <Button
                    variant="outline-success"
                    size="sm"
                    onClick={() => setShowPasswordModal(true)}
                  >
                    <i className="fas fa-key me-1"></i>
                    Change Password
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>

          {/* Right Column - Stats & Achievements */}
          <Col lg={4}>
            {/* Stats Card */}
            <Card className="mb-4 shadow-sm">
              <Card.Header className="bg-light">
                <h6 className="mb-0 fw-semibold">
                  <i className="fas fa-chart-bar me-2 text-info"></i>
                  Activity Stats
                </h6>
              </Card.Header>
              <Card.Body>
                <Row className="text-center">
                  <Col xs={6} className="mb-3">
                    <div className="stat-item">
                      <h4 className="fw-bold text-primary mb-1">{stats.total}</h4>
                      <small className="text-muted">Total Events</small>
                    </div>
                  </Col>
                  <Col xs={6} className="mb-3">
                    <div className="stat-item">
                      <h4 className="fw-bold text-success mb-1">{stats.upcoming}</h4>
                      <small className="text-muted">Upcoming</small>
                    </div>
                  </Col>
                  <Col xs={6}>
                    <div className="stat-item">
                      <h4 className="fw-bold text-warning mb-1">{stats.categories}</h4>
                      <small className="text-muted">Categories</small>
                    </div>
                  </Col>
                  <Col xs={6}>
                    <div className="stat-item">
                      <h4 className="fw-bold text-info mb-1">{stats.past}</h4>
                      <small className="text-muted">Completed</small>
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Achievements Card */}
            <Card className="shadow-sm">
              <Card.Header className="bg-light">
                <h6 className="mb-0 fw-semibold">
                  <i className="fas fa-trophy me-2 text-warning"></i>
                  Achievements
                </h6>
              </Card.Header>
              <Card.Body>
                {achievements.length > 0 ? (
                  <div className="d-flex flex-column gap-2">
                    {achievements.map((achievement, index) => (
                      <div 
                        key={index}
                        className={`d-flex align-items-center p-2 rounded ${achievement.earned ? 'bg-light' : 'bg-light opacity-50'}`}
                      >
                        <i className={`${achievement.icon} text-${achievement.color} me-2`}></i>
                        <div className="flex-grow-1">
                          <div className="fw-semibold small">{achievement.title}</div>
                          <div className="text-muted small">{achievement.description}</div>
                        </div>
                        {achievement.earned && (
                          <i className="fas fa-check-circle text-success"></i>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted text-center mb-0">
                    <i className="fas fa-trophy me-2"></i>
                    No achievements yet. Start participating in events!
                  </p>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Edit Profile Modal */}
        <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg" centered>
          <Modal.Header closeButton>
            <Modal.Title>Edit Profile</Modal.Title>
          </Modal.Header>
          <Form onSubmit={handleProfileUpdate}>
            <Modal.Body>
              <Row>
                <Col md={6} className="mb-3">
                  <Form.Group>
                    <Form.Label>Name *</Form.Label>
                    <Form.Control
                      type="text"
                      value={profileData.name}
                      onChange={(e) => setProfileData(prev => ({ ...prev, name: e.target.value }))}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Group>
                    <Form.Label>Email *</Form.Label>
                    <Form.Control
                      type="email"
                      value={profileData.email}
                      onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Group>
                    <Form.Label>USN</Form.Label>
                    <Form.Control
                      type="text"
                      value={profileData.usn}
                      onChange={(e) => setProfileData(prev => ({ ...prev, usn: e.target.value }))}
                      placeholder="e.g., 1AB21CS001"
                    />
                  </Form.Group>
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Group>
                    <Form.Label>Department</Form.Label>
                    <Form.Select
                      value={profileData.department}
                      onChange={(e) => setProfileData(prev => ({ ...prev, department: e.target.value }))}
                    >
                      <option value="">Select Department</option>
                      {departments.map(dept => (
                        <option key={dept} value={dept}>{dept}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Group>
                    <Form.Label>Year</Form.Label>
                    <Form.Select
                      value={profileData.year}
                      onChange={(e) => setProfileData(prev => ({ ...prev, year: e.target.value }))}
                    >
                      <option value="">Select Year</option>
                      {years.map(year => (
                        <option key={year} value={year}>{year}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={12} className="mb-3">
                  <Form.Group>
                    <Form.Label>Bio</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={3}
                      value={profileData.bio}
                      onChange={(e) => setProfileData(prev => ({ ...prev, bio: e.target.value }))}
                      placeholder="Tell us about yourself..."
                      maxLength={500}
                    />
                    <Form.Text className="text-muted">
                      {profileData.bio.length}/500 characters
                    </Form.Text>
                  </Form.Group>
                </Col>
                <Col md={12} className="mb-3">
                  <Form.Group>
                    <Form.Label>Interests</Form.Label>
                    <div className="d-flex flex-wrap gap-2 mt-2">
                      {interestOptions.map(interest => (
                        <Button
                          key={interest}
                          variant={profileData.interests.includes(interest) ? 'primary' : 'outline-secondary'}
                          size="sm"
                          onClick={() => handleInterestToggle(interest)}
                          type="button"
                        >
                          {interest}
                        </Button>
                      ))}
                    </div>
                  </Form.Group>
                </Col>
              </Row>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={() => setShowEditModal(false)}>
                Cancel
              </Button>
              <Button variant="primary" type="submit" disabled={loading}>
                {loading ? 'Saving...' : 'Save Changes'}
              </Button>
            </Modal.Footer>
          </Form>
        </Modal>

        {/* Change Password Modal */}
        <Modal show={showPasswordModal} onHide={() => setShowPasswordModal(false)} centered>
          <Modal.Header closeButton>
            <Modal.Title>Change Password</Modal.Title>
          </Modal.Header>
          <Form onSubmit={handlePasswordChange}>
            <Modal.Body>
              <Form.Group className="mb-3">
                <Form.Label>Current Password</Form.Label>
                <Form.Control
                  type="password"
                  value={passwordData.currentPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                  required
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>New Password</Form.Label>
                <Form.Control
                  type="password"
                  value={passwordData.newPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                  minLength={6}
                  required
                />
                <Form.Text className="text-muted">
                  Password must be at least 6 characters long.
                </Form.Text>
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Confirm New Password</Form.Label>
                <Form.Control
                  type="password"
                  value={passwordData.confirmPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  required
                />
              </Form.Group>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={() => setShowPasswordModal(false)}>
                Cancel
              </Button>
              <Button variant="success" type="submit" disabled={loading}>
                {loading ? 'Changing...' : 'Change Password'}
              </Button>
            </Modal.Footer>
          </Form>
        </Modal>
      </Container>
    </div>
  );
};

export default Profile;
