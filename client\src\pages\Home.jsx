import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Button, Card } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { eventsAPI } from '../services/api';
import EventCard from '../components/EventCard';
import Loading from '../components/Loading';

const Home = () => {
  const { isAuthenticated } = useAuth();
  const [featuredEvents, setFeaturedEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFeaturedEvents();
  }, []);

  const fetchFeaturedEvents = async () => {
    try {
      const response = await eventsAPI.getAllEvents({ limit: 3 });
      setFeaturedEvents(response.data.events || []);
    } catch (error) {
      console.error('Error fetching featured events:', error);
      // Set mock data as fallback for development
      const mockEvents = [
        {
          _id: 'mock1',
          title: 'Code Quest 2025',
          description: 'A competitive programming contest featuring algorithmic challenges and problem-solving.',
          category: 'Technical',
          eventDate: '2025-02-20T10:00:00Z',
          venue: 'Computer Lab 1',
          maxParticipants: 100,
          registeredCount: 45,
          poster: null
        },
        {
          _id: 'mock2',
          title: 'Cultural Night',
          description: 'An evening of music, dance, and cultural performances by talented students.',
          category: 'Cultural',
          eventDate: '2025-02-22T18:00:00Z',
          venue: 'Main Auditorium',
          maxParticipants: 500,
          registeredCount: 234,
          poster: null
        },
        {
          _id: 'mock3',
          title: 'Football Tournament',
          description: 'Inter-department football championship with exciting matches and prizes.',
          category: 'Sports',
          eventDate: '2025-02-25T14:00:00Z',
          venue: 'Sports Ground',
          maxParticipants: 200,
          registeredCount: 89,
          poster: null
        }
      ];
      setFeaturedEvents(mockEvents);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="home-page">
      {/* Hero Section */}
      <section className="hero-section bg-gradient-primary text-white py-5" style={{ minHeight: '80vh' }}>
        <Container className="h-100">
          <Row className="align-items-center h-100">
            <Col lg={6} className="text-center text-lg-start">
              <h1 className="display-2 fw-bold mb-4">
                Welcome to <span className="text-warning">ALTIUS 2K25</span>
              </h1>
              <p className="lead mb-4">
                The ultimate college fest bringing together technology, culture, sports,
                and creativity. Join us for an unforgettable experience filled with
                competitions, performances, and networking opportunities.
              </p>
              <div className="d-flex flex-column flex-sm-row gap-3 justify-content-center justify-content-lg-start">
                <Button
                  as={Link}
                  to="/events"
                  variant="warning"
                  size="lg"
                  className="text-dark fw-semibold px-4"
                >
                  <i className="fas fa-calendar me-2"></i>
                  View Events
                </Button>
                <Button
                  as={Link}
                  to="/register"
                  variant="outline-light"
                  size="lg"
                  className="px-4"
                >
                  <i className="fas fa-user-plus me-2"></i>
                  Register Now
                </Button>
              </div>
            </Col>
            <Col lg={6} className="text-center mt-5 mt-lg-0">
              <div className="hero-image">
                <i className="fas fa-star text-warning display-1 mb-3"></i>
                <h3 className="text-warning">February 15-25, 2025</h3>
                <p className="text-light">College Campus, Main Auditorium</p>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Stats Section */}
      <section className="stats-section py-5 bg-light">
        <Container>
          <Row className="text-center">
            <Col md={3} className="mb-4">
              <Card className="border-0 shadow-sm h-100">
                <Card.Body>
                  <i className="fas fa-calendar-alt text-warning display-4 mb-3"></i>
                  <h3 className="fw-bold text-primary">50+</h3>
                  <p className="text-muted">Events</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3} className="mb-4">
              <Card className="border-0 shadow-sm h-100">
                <Card.Body>
                  <i className="fas fa-users text-warning display-4 mb-3"></i>
                  <h3 className="fw-bold text-primary">1000+</h3>
                  <p className="text-muted">Participants</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3} className="mb-4">
              <Card className="border-0 shadow-sm h-100">
                <Card.Body>
                  <i className="fas fa-trophy text-warning display-4 mb-3"></i>
                  <h3 className="fw-bold text-primary">₹50K+</h3>
                  <p className="text-muted">Prize Money</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3} className="mb-4">
              <Card className="border-0 shadow-sm h-100">
                <Card.Body>
                  <i className="fas fa-clock text-warning display-4 mb-3"></i>
                  <h3 className="fw-bold text-primary">10</h3>
                  <p className="text-muted">Days of Fun</p>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Featured Events Section */}
      <section className="featured-events py-5">
        <Container>
          <Row className="mb-5">
            <Col className="text-center">
              <h2 className="display-5 fw-bold text-dark mb-3">Featured Events</h2>
              <p className="lead text-muted">
                Don't miss out on these exciting events happening at ALTIUS 2K25
              </p>
            </Col>
          </Row>

          {loading ? (
            <Loading text="Loading featured events..." />
          ) : featuredEvents.length > 0 ? (
            <Row>
              {featuredEvents.map((event) => (
                <Col lg={4} md={6} className="mb-4" key={event._id}>
                  <EventCard event={event} onRegistrationChange={fetchFeaturedEvents} />
                </Col>
              ))}
            </Row>
          ) : (
            <Row>
              <Col className="text-center py-5">
                <i className="fas fa-calendar-alt display-1 text-muted mb-3"></i>
                <h4 className="text-muted">No Featured Events</h4>
                <p className="text-muted">Featured events will appear here soon!</p>
                <Button
                  as={Link}
                  to="/events"
                  variant="primary"
                >
                  View All Events
                </Button>
              </Col>
            </Row>
          )}

          <Row className="mt-4">
            <Col className="text-center">
              <div className="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                <Button
                  as={Link}
                  to="/events"
                  variant="outline-primary"
                  size="lg"
                  className="px-5"
                >
                  <i className="fas fa-list me-2"></i>
                  View All Events
                </Button>
                <Button
                  as={Link}
                  to="/calendar"
                  variant="primary"
                  size="lg"
                  className="px-5"
                >
                  <i className="fas fa-calendar-alt me-2"></i>
                  Event Calendar
                </Button>
              </div>
              <div className="d-flex flex-column flex-sm-row gap-2 justify-content-center mt-3">
                <Button
                  as={Link}
                  to="/faq"
                  variant="outline-secondary"
                  size="sm"
                  className="px-4"
                >
                  <i className="fas fa-question-circle me-2"></i>
                  FAQ
                </Button>
                {isAuthenticated && (
                  <Button
                    as={Link}
                    to="/wishlist"
                    variant="outline-warning"
                    size="sm"
                    className="px-4"
                  >
                    <i className="fas fa-heart me-2"></i>
                    My Wishlist
                  </Button>
                )}
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Categories Section */}
      <section className="categories-section py-5 bg-light">
        <Container>
          <Row className="mb-5">
            <Col className="text-center">
              <h2 className="display-5 fw-bold text-dark mb-3">Event Categories</h2>
              <p className="lead text-muted">
                Explore different categories of events we have to offer
              </p>
            </Col>
          </Row>

          <Row>
            {[
              { name: 'Technical', icon: 'fas fa-code', color: 'primary', description: 'Coding competitions, hackathons, and tech talks' },
              { name: 'Cultural', icon: 'fas fa-music', color: 'success', description: 'Dance, music, drama, and cultural performances' },
              { name: 'Sports', icon: 'fas fa-running', color: 'warning', description: 'Athletic competitions and sports tournaments' },
              { name: 'Gaming', icon: 'fas fa-gamepad', color: 'danger', description: 'Esports tournaments and gaming competitions' },
              { name: 'Literary', icon: 'fas fa-book', color: 'info', description: 'Poetry, storytelling, and creative writing' },
              { name: 'Other', icon: 'fas fa-star', color: 'secondary', description: 'Workshops, seminars, and special events' }
            ].map((category) => (
              <Col lg={4} md={6} className="mb-4" key={category.name}>
                <Card className="border-0 shadow-sm h-100 hover-shadow">
                  <Card.Body className="text-center">
                    <i className={`${category.icon} text-${category.color} display-4 mb-3`}></i>
                    <h5 className="fw-bold text-dark mb-2">{category.name}</h5>
                    <p className="text-muted">{category.description}</p>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="cta-section bg-gradient-primary text-white py-5">
        <Container>
          <Row className="text-center">
            <Col>
              <h2 className="display-5 fw-bold mb-3">Ready to Join the Fun?</h2>
              <p className="lead mb-4">
                Register now and be part of the most exciting college fest of the year!
              </p>
              <Button
                as={Link}
                to="/register"
                variant="warning"
                size="lg"
                className="text-dark fw-semibold px-5"
              >
                <i className="fas fa-rocket me-2"></i>
                Get Started
              </Button>
            </Col>
          </Row>
        </Container>
      </section>
    </div>
  );
};

export default Home;
