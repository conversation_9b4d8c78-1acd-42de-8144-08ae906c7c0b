import React from 'react';
import { Navbar, Nav, Container, But<PERSON> } from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import NotificationDropdown from './NotificationDropdown';

const NavigationBar = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const location = useLocation();

  const handleLogout = () => {
    logout();
  };

  const isActive = (path) => location.pathname === path;

  return (
    <>
      <Navbar bg="dark" variant="dark" expand="lg" fixed="top" className="shadow-sm">
        <Container>
          <Navbar.Brand as={Link} to="/" className="fw-bold fs-3">
            <span className="text-warning">ALTIUS</span>
            <span className="text-light"> 2K25</span>
          </Navbar.Brand>

          <Navbar.Toggle
            aria-controls="basic-navbar-nav"
            className="border-0"
          >
            <i className="fas fa-bars text-warning"></i>
          </Navbar.Toggle>

          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="me-auto ms-lg-4">
              <Nav.Link
                as={Link}
                to="/"
                className={`fw-semibold px-3 ${isActive('/') ? 'text-warning' : ''}`}
              >
                <i className="fas fa-home me-1 d-lg-none"></i>
                Home
              </Nav.Link>
              <Nav.Link
                as={Link}
                to="/events"
                className={`fw-semibold px-3 ${isActive('/events') ? 'text-warning' : ''}`}
              >
                <i className="fas fa-calendar me-1 d-lg-none"></i>
                Events
              </Nav.Link>
              <Nav.Link
                as={Link}
                to="/calendar"
                className={`fw-semibold px-3 ${isActive('/calendar') ? 'text-warning' : ''}`}
              >
                <i className="fas fa-calendar-alt me-1 d-lg-none"></i>
                Calendar
              </Nav.Link>
              <Nav.Link
                as={Link}
                to="/leaderboards"
                className={`fw-semibold px-3 ${isActive('/leaderboards') ? 'text-warning' : ''}`}
              >
                <i className="fas fa-trophy me-1 d-lg-none"></i>
                Leaderboards
              </Nav.Link>
              <Nav.Link
                as={Link}
                to="/gallery"
                className={`fw-semibold px-3 ${isActive('/gallery') ? 'text-warning' : ''}`}
              >
                <i className="fas fa-images me-1 d-lg-none"></i>
                Gallery
              </Nav.Link>
            </Nav>

            <Nav className="align-items-lg-center">
              {isAuthenticated ? (
                <>
                  <Nav.Link
                    as={Link}
                    to="/dashboard"
                    className={`fw-semibold px-3 ${isActive('/dashboard') ? 'text-warning' : ''}`}
                  >
                    <i className="fas fa-tachometer-alt me-1"></i>
                    Dashboard
                  </Nav.Link>
                  <Nav.Link
                    as={Link}
                    to="/profile"
                    className={`fw-semibold px-3 ${isActive('/profile') ? 'text-warning' : ''}`}
                  >
                    <i className="fas fa-user me-1"></i>
                    <span className="d-lg-none">Profile</span>
                    <span className="d-none d-lg-inline">{user?.name}</span>
                  </Nav.Link>
                  <Nav.Link
                    as={Link}
                    to="/wishlist"
                    className={`fw-semibold px-3 ${isActive('/wishlist') ? 'text-warning' : ''}`}
                  >
                    <i className="fas fa-heart me-1"></i>
                    Wishlist
                  </Nav.Link>
                  {user?.isAdmin && (
                    <Nav.Link
                      as={Link}
                      to="/admin"
                      className={`fw-semibold px-3 ${isActive('/admin') ? 'text-warning' : ''}`}
                    >
                      <i className="fas fa-cog me-1"></i>
                      Admin
                    </Nav.Link>
                  )}

                  <div className="d-flex align-items-center ms-lg-2 mt-2 mt-lg-0">
                    <NotificationDropdown />
                    <Button
                      variant="outline-warning"
                      size="sm"
                      onClick={handleLogout}
                      className="ms-2"
                    >
                      <i className="fas fa-sign-out-alt me-1 d-lg-none"></i>
                      Logout
                    </Button>
                  </div>
                </>
              ) : (
                <>
                  <Nav.Link
                    as={Link}
                    to="/login"
                    className={`fw-semibold px-3 ${isActive('/login') ? 'text-warning' : ''}`}
                  >
                    <i className="fas fa-sign-in-alt me-1 d-lg-none"></i>
                    Login
                  </Nav.Link>
                  <Button
                    as={Link}
                    to="/register"
                    variant="warning"
                    size="sm"
                    className="ms-lg-2 mt-2 mt-lg-0 text-dark fw-semibold"
                  >
                    <i className="fas fa-user-plus me-1 d-lg-none"></i>
                    Register
                  </Button>
                </>
              )}
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>
    </>
  );
};

export default NavigationBar;
