import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Badge, Button, Tab, Tabs } from 'react-bootstrap';
import { useAuth } from '../context/AuthContext';
import Leaderboard from '../components/Leaderboard';
import { getUserLevel, getUserPoints, getProgressToNextLevel } from '../utils/pointsSystem';

const Leaderboards = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [userStats, setUserStats] = useState(null);

  // Mock data for demonstration - in real app, this would come from API
  const mockUsers = [
    {
      _id: '1',
      name: '<PERSON>',
      department: 'Computer Science',
      points: 2850,
      weeklyPoints: 450,
      monthlyPoints: 1200,
      categoryPoints: {
        technical: 1500,
        cultural: 800,
        sports: 350,
        gaming: 200
      }
    },
    {
      _id: '2',
      name: '<PERSON>',
      department: 'Electronics',
      points: 2340,
      weeklyPoints: 380,
      monthlyPoints: 980,
      categoryPoints: {
        technical: 1200,
        cultural: 600,
        sports: 540
      }
    },
    {
      _id: '3',
      name: '<PERSON>',
      department: 'Mechanical',
      points: 1890,
      weeklyPoints: 320,
      monthlyPoints: 750,
      categoryPoints: {
        cultural: 900,
        sports: 600,
        technical: 390
      }
    },
    {
      _id: '4',
      name: 'David Wilson',
      department: 'Civil',
      points: 1650,
      weeklyPoints: 280,
      monthlyPoints: 620,
      categoryPoints: {
        sports: 800,
        technical: 500,
        cultural: 350
      }
    },
    {
      _id: '5',
      name: 'Eva Brown',
      department: 'Information Science',
      points: 1420,
      weeklyPoints: 240,
      monthlyPoints: 580,
      categoryPoints: {
        technical: 700,
        literary: 400,
        cultural: 320
      }
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      // Add current user to mock data if authenticated
      let allUsers = [...mockUsers];
      if (user) {
        const userPoints = getUserPoints(user._id) || 150;
        const currentUser = {
          _id: user._id,
          name: user.name,
          department: user.department || 'Unknown',
          points: userPoints,
          weeklyPoints: Math.floor(userPoints * 0.3),
          monthlyPoints: Math.floor(userPoints * 0.7),
          categoryPoints: {
            technical: Math.floor(userPoints * 0.4),
            cultural: Math.floor(userPoints * 0.3),
            sports: Math.floor(userPoints * 0.2),
            gaming: Math.floor(userPoints * 0.1)
          }
        };
        
        // Replace if user already exists, otherwise add
        const existingIndex = allUsers.findIndex(u => u._id === user._id);
        if (existingIndex >= 0) {
          allUsers[existingIndex] = currentUser;
        } else {
          allUsers.push(currentUser);
        }

        setUserStats(currentUser);
      }
      
      setUsers(allUsers);
      setLoading(false);
    }, 1000);
  }, [user]);

  const getUserStatsCard = () => {
    if (!user || !userStats) return null;

    const level = getUserLevel(userStats.points);
    const progress = getProgressToNextLevel(userStats.points);

    return (
      <Card className="mb-4 border-0 shadow-sm bg-gradient-primary text-white">
        <Card.Body>
          <Row className="align-items-center">
            <Col md={3} className="text-center mb-3 mb-md-0">
              <div 
                className="rounded-circle bg-white d-flex align-items-center justify-content-center text-primary fw-bold mx-auto"
                style={{ width: '80px', height: '80px', fontSize: '1.5rem' }}
              >
                {user.name?.charAt(0) || 'U'}
              </div>
              <div className="mt-2">
                <Badge 
                  style={{ backgroundColor: level.color }}
                  className="text-dark"
                >
                  <i className={`${level.icon} me-1`}></i>
                  {level.level}
                </Badge>
              </div>
            </Col>
            <Col md={9}>
              <h4 className="fw-bold mb-1">{user.name}</h4>
              <p className="mb-3 opacity-75">{user.department}</p>
              
              <Row className="g-3">
                <Col sm={6}>
                  <div className="text-center">
                    <h3 className="fw-bold mb-0">{userStats.points}</h3>
                    <small className="opacity-75">Total Points</small>
                  </div>
                </Col>
                <Col sm={6}>
                  <div className="text-center">
                    <h3 className="fw-bold mb-0">#{users.findIndex(u => u._id === user._id) + 1}</h3>
                    <small className="opacity-75">Overall Rank</small>
                  </div>
                </Col>
              </Row>

              {progress.nextLevel !== 'Max Level' && (
                <div className="mt-3">
                  <div className="d-flex justify-content-between small mb-1">
                    <span>Progress to {progress.nextLevel}</span>
                    <span>{progress.pointsToNext} points to go</span>
                  </div>
                  <div className="progress" style={{ height: '8px' }}>
                    <div 
                      className="progress-bar bg-warning"
                      style={{ width: `${progress.progress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </Col>
          </Row>
        </Card.Body>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="leaderboards-page py-5">
        <Container>
          <div className="text-center py-5">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3 text-muted">Loading leaderboards...</p>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div className="leaderboards-page py-3 py-md-5">
      <Container fluid className="px-3 px-md-4">
        {/* Header */}
        <Row className="mb-4">
          <Col className="text-center">
            <h1 className="display-5 fw-bold text-dark mb-2">
              <i className="fas fa-trophy text-warning me-3"></i>
              Leaderboards
            </h1>
            <p className="text-muted">Compete with fellow participants and climb the ranks!</p>
          </Col>
        </Row>

        {/* User Stats Card */}
        {getUserStatsCard()}

        {/* Leaderboard Tabs */}
        <Card className="shadow-sm">
          <Card.Body className="p-0">
            <Tabs defaultActiveKey="overall" className="nav-fill">
              <Tab 
                eventKey="overall" 
                title={
                  <span>
                    <i className="fas fa-trophy me-2"></i>
                    Overall
                  </span>
                }
              >
                <div className="p-3">
                  <Leaderboard 
                    users={users} 
                    category="overall" 
                    timeframe="all" 
                  />
                </div>
              </Tab>

              <Tab 
                eventKey="technical" 
                title={
                  <span>
                    <i className="fas fa-code me-2"></i>
                    Technical
                  </span>
                }
              >
                <div className="p-3">
                  <Leaderboard 
                    users={users} 
                    category="technical" 
                    timeframe="all" 
                  />
                </div>
              </Tab>

              <Tab 
                eventKey="cultural" 
                title={
                  <span>
                    <i className="fas fa-music me-2"></i>
                    Cultural
                  </span>
                }
              >
                <div className="p-3">
                  <Leaderboard 
                    users={users} 
                    category="cultural" 
                    timeframe="all" 
                  />
                </div>
              </Tab>

              <Tab 
                eventKey="sports" 
                title={
                  <span>
                    <i className="fas fa-running me-2"></i>
                    Sports
                  </span>
                }
              >
                <div className="p-3">
                  <Leaderboard 
                    users={users} 
                    category="sports" 
                    timeframe="all" 
                  />
                </div>
              </Tab>

              <Tab 
                eventKey="weekly" 
                title={
                  <span>
                    <i className="fas fa-calendar-week me-2"></i>
                    This Week
                  </span>
                }
              >
                <div className="p-3">
                  <Leaderboard 
                    users={users} 
                    category="overall" 
                    timeframe="weekly" 
                  />
                </div>
              </Tab>
            </Tabs>
          </Card.Body>
        </Card>

        {/* Points Info */}
        <Row className="mt-5">
          <Col>
            <Card className="bg-light border-0">
              <Card.Body>
                <h5 className="fw-semibold mb-3">
                  <i className="fas fa-info-circle me-2 text-info"></i>
                  How to Earn Points
                </h5>
                <Row className="g-3">
                  <Col md={6} lg={3}>
                    <div className="text-center">
                      <i className="fas fa-user-plus display-6 text-success mb-2"></i>
                      <h6>Event Registration</h6>
                      <small className="text-muted">10-35 points per event</small>
                    </div>
                  </Col>
                  <Col md={6} lg={3}>
                    <div className="text-center">
                      <i className="fas fa-medal display-6 text-warning mb-2"></i>
                      <h6>Event Participation</h6>
                      <small className="text-muted">50-75 points per event</small>
                    </div>
                  </Col>
                  <Col md={6} lg={3}>
                    <div className="text-center">
                      <i className="fas fa-trophy display-6 text-danger mb-2"></i>
                      <h6>Winning Events</h6>
                      <small className="text-muted">100-200 points</small>
                    </div>
                  </Col>
                  <Col md={6} lg={3}>
                    <div className="text-center">
                      <i className="fas fa-star display-6 text-info mb-2"></i>
                      <h6>Achievements</h6>
                      <small className="text-muted">15-50 points each</small>
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default Leaderboards;
