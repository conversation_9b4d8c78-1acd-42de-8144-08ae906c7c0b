import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Alert, Button, Form } from 'react-bootstrap';
import { useAuth } from '../context/AuthContext';
import { eventsAPI } from '../services/api';
import EventCard from '../components/EventCard';
import Loading from '../components/Loading';
import { getCategoryColor } from '../utils/helpers';

const Wishlist = () => {
  const { user, isAuthenticated } = useAuth();
  const [wishlistedEvents, setWishlistedEvents] = useState([]);
  const [allEvents, setAllEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('dateAdded'); // 'dateAdded', 'eventDate', 'title'
  const [filterCategory, setFilterCategory] = useState('All');

  const categories = ['All', 'Technical', 'Cultural', 'Sports', 'Literary', 'Gaming', 'Other'];

  useEffect(() => {
    if (isAuthenticated) {
      fetchWishlistedEvents();
    } else {
      setLoading(false);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    // Listen for wishlist updates
    const handleWishlistUpdate = () => {
      fetchWishlistedEvents();
    };

    window.addEventListener('wishlistUpdated', handleWishlistUpdate);
    return () => window.removeEventListener('wishlistUpdated', handleWishlistUpdate);
  }, []);

  const fetchWishlistedEvents = async () => {
    try {
      setLoading(true);
      
      // Get all events
      const eventsResponse = await eventsAPI.getAllEvents({ limit: 100 });
      const events = eventsResponse.data.events;
      setAllEvents(events);
      
      // Get user's wishlist from localStorage
      const wishlistKey = `wishlist_${user.id}`;
      const wishlistIds = JSON.parse(localStorage.getItem(wishlistKey) || '[]');
      
      // Filter events that are in wishlist
      const wishlisted = events.filter(event => wishlistIds.includes(event._id));
      setWishlistedEvents(wishlisted);
      
    } catch (error) {
      console.error('Error fetching wishlisted events:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClearWishlist = () => {
    if (window.confirm('Are you sure you want to clear your entire wishlist?')) {
      const wishlistKey = `wishlist_${user.id}`;
      localStorage.removeItem(wishlistKey);
      setWishlistedEvents([]);
      
      // Dispatch update event
      window.dispatchEvent(new CustomEvent('wishlistUpdated', {
        detail: { cleared: true }
      }));
    }
  };

  const getSortedAndFilteredEvents = () => {
    let filtered = wishlistedEvents;
    
    // Apply category filter
    if (filterCategory !== 'All') {
      filtered = filtered.filter(event => event.category === filterCategory);
    }
    
    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'eventDate':
          return new Date(a.eventDate) - new Date(b.eventDate);
        case 'title':
          return a.title.localeCompare(b.title);
        case 'dateAdded':
        default:
          // For date added, we'll use the order in localStorage (most recent first)
          const wishlistKey = `wishlist_${user.id}`;
          const wishlistIds = JSON.parse(localStorage.getItem(wishlistKey) || '[]');
          return wishlistIds.indexOf(b._id) - wishlistIds.indexOf(a._id);
      }
    });
    
    return sorted;
  };

  const getWishlistStats = () => {
    const now = new Date();
    const upcoming = wishlistedEvents.filter(event => new Date(event.eventDate) > now);
    const categories = new Set(wishlistedEvents.map(event => event.category));
    
    return {
      total: wishlistedEvents.length,
      upcoming: upcoming.length,
      categories: categories.size
    };
  };

  if (!isAuthenticated) {
    return (
      <div className="wishlist-page py-5">
        <Container>
          <Row className="justify-content-center">
            <Col md={6} className="text-center">
              <Card>
                <Card.Body className="py-5">
                  <i className="fas fa-heart display-1 text-muted mb-4"></i>
                  <h3 className="fw-bold mb-3">Login Required</h3>
                  <p className="text-muted mb-4">
                    Please login to view and manage your event wishlist.
                  </p>
                  <Button variant="primary" href="/login">
                    Login Now
                  </Button>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </div>
    );
  }

  if (loading) {
    return <Loading fullScreen text="Loading your wishlist..." />;
  }

  const sortedEvents = getSortedAndFilteredEvents();
  const stats = getWishlistStats();

  return (
    <div className="wishlist-page py-3 py-md-5">
      <Container fluid className="px-3 px-md-4">
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h1 className="display-5 fw-bold text-dark mb-2">
                  <i className="fas fa-heart text-danger me-3"></i>
                  My Wishlist
                </h1>
                <p className="text-muted">Events you've saved for later</p>
              </div>
              {wishlistedEvents.length > 0 && (
                <Button
                  variant="outline-danger"
                  onClick={handleClearWishlist}
                >
                  <i className="fas fa-trash me-1"></i>
                  Clear All
                </Button>
              )}
            </div>
          </Col>
        </Row>

        {wishlistedEvents.length === 0 ? (
          <Row className="justify-content-center">
            <Col md={6} className="text-center">
              <Card>
                <Card.Body className="py-5">
                  <i className="fas fa-heart-broken display-1 text-muted mb-4"></i>
                  <h3 className="fw-bold mb-3">Your Wishlist is Empty</h3>
                  <p className="text-muted mb-4">
                    Start adding events to your wishlist by clicking the heart icon on any event.
                  </p>
                  <Button variant="primary" href="/events">
                    <i className="fas fa-calendar me-2"></i>
                    Browse Events
                  </Button>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        ) : (
          <>
            {/* Stats Cards */}
            <Row className="mb-4">
              <Col md={4} className="mb-3">
                <Card className="bg-primary text-white h-100">
                  <Card.Body className="text-center">
                    <i className="fas fa-heart display-6 mb-2"></i>
                    <h4 className="fw-bold">{stats.total}</h4>
                    <small>Wishlisted Events</small>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={4} className="mb-3">
                <Card className="bg-success text-white h-100">
                  <Card.Body className="text-center">
                    <i className="fas fa-calendar-plus display-6 mb-2"></i>
                    <h4 className="fw-bold">{stats.upcoming}</h4>
                    <small>Upcoming Events</small>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={4} className="mb-3">
                <Card className="bg-info text-white h-100">
                  <Card.Body className="text-center">
                    <i className="fas fa-tags display-6 mb-2"></i>
                    <h4 className="fw-bold">{stats.categories}</h4>
                    <small>Categories</small>
                  </Card.Body>
                </Card>
              </Col>
            </Row>

            {/* Filters and Sorting */}
            <Row className="mb-4">
              <Col md={6} className="mb-3">
                <Form.Select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'All' ? 'All Categories' : category}
                    </option>
                  ))}
                </Form.Select>
              </Col>
              <Col md={6} className="mb-3">
                <Form.Select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                >
                  <option value="dateAdded">Recently Added</option>
                  <option value="eventDate">Event Date</option>
                  <option value="title">Event Title</option>
                </Form.Select>
              </Col>
            </Row>

            {/* Events Grid */}
            {sortedEvents.length === 0 ? (
              <Alert variant="info" className="text-center">
                <i className="fas fa-filter me-2"></i>
                No events found matching your filters.
              </Alert>
            ) : (
              <Row>
                {sortedEvents.map((event) => (
                  <Col xl={3} lg={4} md={6} sm={6} xs={12} className="mb-4" key={event._id}>
                    <EventCard 
                      event={event} 
                      onRegistrationChange={fetchWishlistedEvents}
                    />
                  </Col>
                ))}
              </Row>
            )}
          </>
        )}
      </Container>
    </div>
  );
};

export default Wishlist;
