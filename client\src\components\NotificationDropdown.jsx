import React, { useState } from 'react';
import { Dropdown, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Alert } from 'react-bootstrap';
import { useNotifications } from '../context/NotificationContext';

// Simple time ago utility
const formatDistanceToNow = (date) => {
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);

  if (diffInSeconds < 60) return 'just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;
  return date.toLocaleDateString();
};

const NotificationDropdown = () => {
  const { 
    notifications, 
    unreadCount, 
    markAsRead, 
    markAllAsRead, 
    removeNotification, 
    clearAll 
  } = useNotifications();
  
  const [show, setShow] = useState(false);

  const handleNotificationClick = (notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
  };

  const getNotificationIcon = (notification) => {
    if (notification.icon) {
      return notification.icon;
    }
    
    switch (notification.type) {
      case 'success':
        return 'fas fa-check-circle text-success';
      case 'error':
        return 'fas fa-exclamation-circle text-danger';
      case 'warning':
        return 'fas fa-exclamation-triangle text-warning';
      case 'info':
      default:
        return 'fas fa-info-circle text-info';
    }
  };

  const getNotificationColor = (notification) => {
    switch (notification.type) {
      case 'success':
        return 'success';
      case 'error':
        return 'danger';
      case 'warning':
        return 'warning';
      case 'info':
      default:
        return 'info';
    }
  };

  const recentNotifications = notifications.slice(0, 10);

  return (
    <Dropdown show={show} onToggle={setShow} align="end">
      <Dropdown.Toggle
        variant="outline-light"
        className="position-relative border-0"
        style={{ boxShadow: 'none' }}
      >
        <i className="fas fa-bell"></i>
        {unreadCount > 0 && (
          <Badge
            bg="danger"
            pill
            className="position-absolute top-0 start-100 translate-middle"
            style={{ fontSize: '0.6rem' }}
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Dropdown.Toggle>

      <Dropdown.Menu className="notification-dropdown" style={{ width: '350px', maxHeight: '500px', overflowY: 'auto' }}>
        <div className="px-3 py-2 border-bottom">
          <div className="d-flex justify-content-between align-items-center">
            <h6 className="mb-0 fw-semibold">Notifications</h6>
            {unreadCount > 0 && (
              <Button
                variant="link"
                size="sm"
                className="text-decoration-none p-0"
                onClick={markAllAsRead}
              >
                Mark all read
              </Button>
            )}
          </div>
        </div>

        {notifications.length === 0 ? (
          <div className="px-3 py-4 text-center">
            <i className="fas fa-bell-slash display-6 text-muted mb-3"></i>
            <p className="text-muted mb-0">No notifications yet</p>
          </div>
        ) : (
          <>
            <div className="notification-list">
              {recentNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`notification-item px-3 py-2 border-bottom cursor-pointer ${
                    !notification.read ? 'bg-light' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                  style={{ cursor: 'pointer' }}
                >
                  <div className="d-flex align-items-start">
                    <div className="me-2 mt-1">
                      <i className={getNotificationIcon(notification)}></i>
                    </div>
                    <div className="flex-grow-1">
                      <div className="d-flex justify-content-between align-items-start">
                        <div>
                          {notification.title && (
                            <div className="fw-semibold small text-dark">
                              {notification.title}
                            </div>
                          )}
                          <div className={`small ${notification.read ? 'text-muted' : 'text-dark'}`}>
                            {notification.message}
                          </div>
                          <div className="text-muted small">
                            {formatDistanceToNow(notification.timestamp, { addSuffix: true })}
                          </div>
                        </div>
                        <div className="d-flex align-items-center">
                          {!notification.read && (
                            <div
                              className="bg-primary rounded-circle"
                              style={{ width: '8px', height: '8px' }}
                            ></div>
                          )}
                          <Button
                            variant="link"
                            size="sm"
                            className="text-muted p-0 ms-2"
                            onClick={(e) => {
                              e.stopPropagation();
                              removeNotification(notification.id);
                            }}
                          >
                            <i className="fas fa-times"></i>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {notifications.length > 10 && (
              <div className="px-3 py-2 border-top text-center">
                <small className="text-muted">
                  Showing 10 of {notifications.length} notifications
                </small>
              </div>
            )}

            <div className="px-3 py-2 border-top">
              <div className="d-flex gap-2">
                <Button
                  variant="outline-secondary"
                  size="sm"
                  className="flex-grow-1"
                  onClick={clearAll}
                >
                  Clear All
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  className="flex-grow-1"
                  href="/notifications"
                  onClick={() => setShow(false)}
                >
                  View All
                </Button>
              </div>
            </div>
          </>
        )}
      </Dropdown.Menu>
    </Dropdown>
  );
};

export default NotificationDropdown;
