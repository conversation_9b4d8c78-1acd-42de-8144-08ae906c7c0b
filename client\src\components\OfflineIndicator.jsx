import React, { useState, useEffect } from 'react';
import { Alert, Button, Toast, ToastContainer } from 'react-bootstrap';
import { usePWA } from '../hooks/usePWA';

const OfflineIndicator = () => {
  const { isOnline } = usePWA();
  const [showOfflineAlert, setShowOfflineAlert] = useState(false);
  const [showOnlineToast, setShowOnlineToast] = useState(false);
  const [wasOffline, setWasOffline] = useState(false);

  useEffect(() => {
    if (!isOnline) {
      setShowOfflineAlert(true);
      setWasOffline(true);
    } else {
      setShowOfflineAlert(false);
      
      // Show "back online" toast if user was previously offline
      if (wasOffline) {
        setShowOnlineToast(true);
        setWasOffline(false);
        
        // Auto-hide toast after 3 seconds
        setTimeout(() => {
          setShowOnlineToast(false);
        }, 3000);
      }
    }
  }, [isOnline, wasOffline]);

  const handleRetry = () => {
    // Force a page refresh to retry network requests
    window.location.reload();
  };

  return (
    <>
      {/* Offline Alert Banner */}
      {showOfflineAlert && (
        <Alert 
          variant="warning" 
          className="position-fixed top-0 start-0 end-0 m-0 text-center border-0 rounded-0"
          style={{ zIndex: 1060 }}
        >
          <div className="d-flex align-items-center justify-content-center">
            <i className="fas fa-wifi-slash me-2"></i>
            <span className="me-3">
              <strong>You're offline</strong> - Some features may be limited
            </span>
            <Button 
              variant="outline-warning" 
              size="sm"
              onClick={handleRetry}
            >
              <i className="fas fa-redo me-1"></i>
              Retry
            </Button>
          </div>
        </Alert>
      )}

      {/* Back Online Toast */}
      <ToastContainer position="top-end" className="p-3" style={{ zIndex: 1070 }}>
        <Toast 
          show={showOnlineToast} 
          onClose={() => setShowOnlineToast(false)}
          bg="success"
          delay={3000}
          autohide
        >
          <Toast.Header closeButton={false}>
            <i className="fas fa-wifi text-success me-2"></i>
            <strong className="me-auto">Back Online!</strong>
          </Toast.Header>
          <Toast.Body className="text-white">
            Your internet connection has been restored.
          </Toast.Body>
        </Toast>
      </ToastContainer>
    </>
  );
};

export default OfflineIndicator;
