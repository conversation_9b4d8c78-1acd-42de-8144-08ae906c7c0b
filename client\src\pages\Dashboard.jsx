import React, { useState, useEffect } from 'react';
import { Con<PERSON>er, Row, Col, Card, Button, Badge, Alert } from 'react-bootstrap';
import { useAuth } from '../context/AuthContext';
import { authAPI, eventsAPI } from '../services/api';
import { formatDate, getCategoryColor } from '../utils/helpers';
import Loading from '../components/Loading';
import { toast } from 'react-toastify';

const Dashboard = () => {
  const { user } = useAuth();
  const [registeredEvents, setRegisteredEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    try {
      const response = await authAPI.getProfile();
      setRegisteredEvents(response.data.user.registeredEvents || []);
    } catch (error) {
      console.error('Error fetching user data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleUnregister = async (eventId) => {
    try {
      await eventsAPI.unregisterFromEvent(eventId);
      toast.success('Successfully unregistered from the event');
      fetchUserData(); // Refresh data
    } catch (error) {
      const message = error.response?.data?.message || 'Unregistration failed';
      toast.error(message);
    }
  };

  if (loading) {
    return <Loading fullScreen text="Loading your dashboard..." />;
  }

  return (
    <div className="dashboard-page py-3 py-md-5">
      <Container fluid className="px-3 px-md-4">
        {/* Welcome Section */}
        <Row className="mb-5">
          <Col>
            <Card className="bg-gradient-primary text-white border-0 shadow">
              <Card.Body className="p-4">
                <Row className="align-items-center">
                  <Col md={8}>
                    <h2 className="fw-bold mb-2">Welcome back, {user?.name}!</h2>
                    <p className="mb-0 opacity-75">
                      Manage your event registrations and stay updated with ALTIUS 2K25
                    </p>
                  </Col>
                  <Col md={4} className="text-md-end">
                    <div className="dashboard-stats">
                      <h3 className="fw-bold mb-1">{registeredEvents.length}</h3>
                      <small className="opacity-75">Registered Events</small>
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* User Info */}
        <Row className="mb-4">
          <Col lg={6} md={12} className="mb-3 mb-lg-0">
            <Card className="h-100 shadow-sm">
              <Card.Header className="bg-light">
                <h5 className="mb-0 fw-semibold">
                  <i className="fas fa-user me-2 text-primary"></i>
                  Profile Information
                </h5>
              </Card.Header>
              <Card.Body>
                <div className="mb-3">
                  <strong>Name:</strong> {user?.name}
                </div>
                <div className="mb-3">
                  <strong>Email:</strong> {user?.email}
                </div>
                <div className="mb-3">
                  <strong>Account Type:</strong>{' '}
                  <Badge bg={user?.isAdmin ? 'danger' : 'primary'}>
                    {user?.isAdmin ? 'Admin' : 'Student'}
                  </Badge>
                </div>
                <div>
                  <strong>Member Since:</strong> {formatDate(user?.createdAt)}
                </div>
              </Card.Body>
            </Card>
          </Col>
          <Col lg={6} md={12}>
            <Card className="h-100 shadow-sm">
              <Card.Header className="bg-light">
                <h5 className="mb-0 fw-semibold">
                  <i className="fas fa-chart-bar me-2 text-success"></i>
                  Quick Stats
                </h5>
              </Card.Header>
              <Card.Body>
                <Row className="text-center">
                  <Col>
                    <div className="stat-item mb-3">
                      <h4 className="fw-bold text-primary mb-1">{registeredEvents.length}</h4>
                      <small className="text-muted">Total Registrations</small>
                    </div>
                  </Col>
                  <Col>
                    <div className="stat-item mb-3">
                      <h4 className="fw-bold text-success mb-1">
                        {registeredEvents.filter(event => new Date(event.eventDate) > new Date()).length}
                      </h4>
                      <small className="text-muted">Upcoming Events</small>
                    </div>
                  </Col>
                </Row>
                <Row className="text-center">
                  <Col>
                    <div className="stat-item">
                      <h4 className="fw-bold text-warning mb-1">
                        {new Set(registeredEvents.map(event => event.category)).size}
                      </h4>
                      <small className="text-muted">Categories</small>
                    </div>
                  </Col>
                  <Col>
                    <div className="stat-item">
                      <h4 className="fw-bold text-info mb-1">
                        {registeredEvents.filter(event => new Date(event.eventDate) < new Date()).length}
                      </h4>
                      <small className="text-muted">Past Events</small>
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Registered Events */}
        <Row>
          <Col>
            <Card className="shadow-sm">
              <Card.Header className="bg-light d-flex justify-content-between align-items-center">
                <h5 className="mb-0 fw-semibold">
                  <i className="fas fa-calendar me-2 text-warning"></i>
                  My Registered Events
                </h5>
                <Badge bg="secondary">{registeredEvents.length} events</Badge>
              </Card.Header>
              <Card.Body>
                {registeredEvents.length === 0 ? (
                  <div className="text-center py-5">
                    <i className="fas fa-calendar-times display-1 text-muted mb-3"></i>
                    <h4 className="text-muted">No Events Registered</h4>
                    <p className="text-muted mb-4">
                      You haven't registered for any events yet. Explore our exciting events and register now!
                    </p>
                    <Button variant="warning" href="/events" className="text-dark fw-semibold">
                      <i className="fas fa-search me-2"></i>
                      Browse Events
                    </Button>
                  </div>
                ) : (
                  <Row>
                    {registeredEvents.map((event) => {
                      const isUpcoming = new Date(event.eventDate) > new Date();
                      return (
                        <Col xl={6} lg={12} md={6} sm={12} className="mb-4" key={event._id}>
                          <Card className="border h-100">
                            <Card.Body>
                              <div className="d-flex justify-content-between align-items-start mb-3">
                                <h6 className="fw-bold mb-0">{event.title}</h6>
                                <Badge bg={getCategoryColor(event.category)}>
                                  {event.category}
                                </Badge>
                              </div>
                              
                              <div className="mb-3">
                                <small className="text-muted d-block">
                                  <i className="fas fa-calendar me-1"></i>
                                  {formatDate(event.eventDate)}
                                </small>
                                <small className="text-muted d-block">
                                  <i className="fas fa-map-marker-alt me-1"></i>
                                  {event.venue}
                                </small>
                                <small className={`d-block ${isUpcoming ? 'text-success' : 'text-muted'}`}>
                                  <i className="fas fa-clock me-1"></i>
                                  {isUpcoming ? 'Upcoming' : 'Completed'}
                                </small>
                              </div>

                              {isUpcoming && (
                                <Button
                                  variant="outline-danger"
                                  size="sm"
                                  onClick={() => handleUnregister(event._id)}
                                  className="w-100"
                                >
                                  <i className="fas fa-times me-1"></i>
                                  Unregister
                                </Button>
                              )}
                            </Card.Body>
                          </Card>
                        </Col>
                      );
                    })}
                  </Row>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Quick Actions */}
        {registeredEvents.length > 0 && (
          <Row className="mt-4">
            <Col>
              <Alert variant="info" className="mb-0">
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <strong>Reminder:</strong> Make sure to check event details and arrive on time for your registered events.
                  </div>
                  <Button variant="outline-info" size="sm" href="/events">
                    View All Events
                  </Button>
                </div>
              </Alert>
            </Col>
          </Row>
        )}
      </Container>
    </div>
  );
};

export default Dashboard;
