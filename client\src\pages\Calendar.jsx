import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Badge, Alert } from 'react-bootstrap';
import { eventsAPI } from '../services/api';
import EventCalendar from '../components/EventCalendar';
import EventCard from '../components/EventCard';
import Loading from '../components/Loading';
import { getCategoryColor } from '../utils/helpers';

const Calendar = () => {
  const [events, setEvents] = useState([]);
  const [filteredEvents, setFilteredEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [viewMode, setViewMode] = useState('calendar'); // 'calendar' or 'list'
  const [categoryFilter, setCategoryFilter] = useState('All');

  const categories = ['All', 'Technical', 'Cultural', 'Sports', 'Literary', 'Gaming', 'Other'];

  useEffect(() => {
    fetchEvents();
  }, []);

  useEffect(() => {
    filterEvents();
  }, [events, categoryFilter]);

  const fetchEvents = async () => {
    try {
      const response = await eventsAPI.getAllEvents({ limit: 100 });
      setEvents(response.data.events);
    } catch (error) {
      console.error('Error fetching events:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterEvents = () => {
    if (categoryFilter === 'All') {
      setFilteredEvents(events);
    } else {
      setFilteredEvents(events.filter(event => event.category === categoryFilter));
    }
  };

  const handleEventSelect = (event) => {
    setSelectedEvent(event);
  };

  const getUpcomingEvents = () => {
    const now = new Date();
    return filteredEvents
      .filter(event => new Date(event.eventDate) > now)
      .sort((a, b) => new Date(a.eventDate) - new Date(b.eventDate))
      .slice(0, 5);
  };

  const getEventStats = () => {
    const now = new Date();
    const upcoming = filteredEvents.filter(event => new Date(event.eventDate) > now);
    const past = filteredEvents.filter(event => new Date(event.eventDate) <= now);
    const today = filteredEvents.filter(event => {
      const eventDate = new Date(event.eventDate);
      const todayDate = new Date();
      return eventDate.toDateString() === todayDate.toDateString();
    });

    return { upcoming: upcoming.length, past: past.length, today: today.length };
  };

  if (loading) {
    return <Loading fullScreen text="Loading calendar..." />;
  }

  const stats = getEventStats();
  const upcomingEvents = getUpcomingEvents();

  return (
    <div className="calendar-page py-3 py-md-5">
      <Container fluid className="px-3 px-md-4">
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <h1 className="display-5 fw-bold text-dark mb-2">Event Calendar</h1>
            <p className="text-muted">View all ALTIUS 2K25 events in calendar format</p>
          </Col>
        </Row>

        {/* Stats Cards */}
        <Row className="mb-4">
          <Col md={3} sm={6} className="mb-3">
            <Card className="bg-primary text-white h-100">
              <Card.Body className="text-center">
                <i className="fas fa-calendar-check display-6 mb-2"></i>
                <h4 className="fw-bold">{stats.today}</h4>
                <small>Today's Events</small>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} sm={6} className="mb-3">
            <Card className="bg-success text-white h-100">
              <Card.Body className="text-center">
                <i className="fas fa-calendar-plus display-6 mb-2"></i>
                <h4 className="fw-bold">{stats.upcoming}</h4>
                <small>Upcoming Events</small>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} sm={6} className="mb-3">
            <Card className="bg-info text-white h-100">
              <Card.Body className="text-center">
                <i className="fas fa-calendar display-6 mb-2"></i>
                <h4 className="fw-bold">{filteredEvents.length}</h4>
                <small>Total Events</small>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} sm={6} className="mb-3">
            <Card className="bg-warning text-dark h-100">
              <Card.Body className="text-center">
                <i className="fas fa-calendar-times display-6 mb-2"></i>
                <h4 className="fw-bold">{stats.past}</h4>
                <small>Past Events</small>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Controls */}
        <Row className="mb-4">
          <Col md={6} className="mb-3">
            <Form.Select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'All' ? 'All Categories' : category}
                </option>
              ))}
            </Form.Select>
          </Col>
          <Col md={6} className="mb-3">
            <div className="d-flex gap-2">
              <Button
                variant={viewMode === 'calendar' ? 'primary' : 'outline-primary'}
                onClick={() => setViewMode('calendar')}
                className="flex-fill"
              >
                <i className="fas fa-calendar me-1"></i>
                Calendar View
              </Button>
              <Button
                variant={viewMode === 'list' ? 'primary' : 'outline-primary'}
                onClick={() => setViewMode('list')}
                className="flex-fill"
              >
                <i className="fas fa-list me-1"></i>
                List View
              </Button>
            </div>
          </Col>
        </Row>

        {/* Main Content */}
        <Row>
          <Col lg={8} className="mb-4">
            {viewMode === 'calendar' ? (
              <EventCalendar 
                events={filteredEvents} 
                onEventSelect={handleEventSelect}
              />
            ) : (
              <Card>
                <Card.Header>
                  <h5 className="mb-0 fw-semibold">
                    <i className="fas fa-list me-2"></i>
                    Events List
                  </h5>
                </Card.Header>
                <Card.Body>
                  {filteredEvents.length === 0 ? (
                    <Alert variant="info" className="text-center">
                      <i className="fas fa-info-circle me-2"></i>
                      No events found for the selected category.
                    </Alert>
                  ) : (
                    <Row>
                      {filteredEvents.map((event) => (
                        <Col lg={6} className="mb-3" key={event._id}>
                          <EventCard 
                            event={event} 
                            onRegistrationChange={fetchEvents}
                          />
                        </Col>
                      ))}
                    </Row>
                  )}
                </Card.Body>
              </Card>
            )}
          </Col>

          {/* Sidebar */}
          <Col lg={4}>
            {/* Selected Event Details */}
            {selectedEvent && (
              <Card className="mb-4">
                <Card.Header>
                  <h6 className="mb-0 fw-semibold">
                    <i className="fas fa-info-circle me-2"></i>
                    Selected Event
                  </h6>
                </Card.Header>
                <Card.Body>
                  <EventCard 
                    event={selectedEvent} 
                    onRegistrationChange={fetchEvents}
                    compact={true}
                  />
                </Card.Body>
              </Card>
            )}

            {/* Upcoming Events */}
            <Card className="mb-4">
              <Card.Header>
                <h6 className="mb-0 fw-semibold">
                  <i className="fas fa-clock me-2"></i>
                  Upcoming Events
                </h6>
              </Card.Header>
              <Card.Body>
                {upcomingEvents.length === 0 ? (
                  <p className="text-muted text-center mb-0">
                    <i className="fas fa-calendar-times me-2"></i>
                    No upcoming events
                  </p>
                ) : (
                  <div className="d-flex flex-column gap-2">
                    {upcomingEvents.map((event) => (
                      <div 
                        key={event._id} 
                        className="d-flex align-items-center p-2 border rounded cursor-pointer hover-shadow"
                        onClick={() => handleEventSelect(event)}
                      >
                        <Badge bg={getCategoryColor(event.category)} className="me-2">
                          {event.category}
                        </Badge>
                        <div className="flex-grow-1">
                          <div className="fw-semibold small">{event.title}</div>
                          <div className="text-muted small">
                            {new Date(event.eventDate).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </Card.Body>
            </Card>

            {/* Category Legend */}
            <Card>
              <Card.Header>
                <h6 className="mb-0 fw-semibold">
                  <i className="fas fa-palette me-2"></i>
                  Category Colors
                </h6>
              </Card.Header>
              <Card.Body>
                <div className="d-flex flex-column gap-2">
                  {categories.filter(cat => cat !== 'All').map((category) => (
                    <div key={category} className="d-flex align-items-center">
                      <Badge bg={getCategoryColor(category)} className="me-2">
                        {category}
                      </Badge>
                      <small className="text-muted">{category} Events</small>
                    </div>
                  ))}
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default Calendar;
